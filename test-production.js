/**
 * Production Test Script for Universal AI Brain
 * Tests core functionality with real MongoDB Atlas and OpenAI credentials
 */

const { MongoClient } = require('mongodb');

async function testProductionReadiness() {
  console.log('🧪 Starting Universal AI Brain Production Tests...\n');

  const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://romiluz:<EMAIL>/?retryWrites=true&w=majority&appName=agents';
  const databaseName = process.env.DATABASE_NAME || 'universal_ai_brain_test';
  const openaiApiKey = process.env.OPENAI_API_KEY || '';

  let client;
  let testsPassed = 0;
  let testsTotal = 0;

  try {
    // Test 1: MongoDB Atlas Connection
    testsTotal++;
    console.log('📊 Test 1: MongoDB Atlas Connection');
    
    client = new MongoClient(mongoUri, {
      maxPoolSize: 5,
      minPoolSize: 1,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      retryReads: true
    });

    await client.connect();
    const db = client.db(databaseName);
    
    const result = await db.admin().ping();
    if (result.ok === 1) {
      console.log('✅ MongoDB Atlas connection successful');
      testsPassed++;
    } else {
      console.log('❌ MongoDB Atlas ping failed');
    }

    // Test 2: Collection Operations
    testsTotal++;
    console.log('\n📊 Test 2: Collection Operations');
    
    await db.createCollection('test_memory');
    await db.createCollection('test_context');
    
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    if (collectionNames.includes('test_memory') && collectionNames.includes('test_context')) {
      console.log('✅ Collection creation successful');
      testsPassed++;
    } else {
      console.log('❌ Collection creation failed');
    }

    // Clean up test collections
    await db.collection('test_memory').drop();
    await db.collection('test_context').drop();

    // Test 3: CRUD Operations
    testsTotal++;
    console.log('\n📊 Test 3: CRUD Operations');
    
    const collection = db.collection('test_crud');
    
    // Create
    const insertResult = await collection.insertOne({
      id: 'test_1',
      content: 'Universal AI Brain test document',
      metadata: {
        type: 'test',
        framework: 'universal',
        created: new Date()
      }
    });
    
    // Read
    const document = await collection.findOne({ id: 'test_1' });
    
    // Update
    const updateResult = await collection.updateOne(
      { id: 'test_1' },
      { $set: { content: 'Updated Universal AI Brain test document' } }
    );
    
    // Delete
    const deleteResult = await collection.deleteOne({ id: 'test_1' });
    
    if (insertResult.insertedId && document && updateResult.modifiedCount === 1 && deleteResult.deletedCount === 1) {
      console.log('✅ CRUD operations successful');
      testsPassed++;
    } else {
      console.log('❌ CRUD operations failed');
    }

    // Clean up
    await collection.drop();

  } catch (error) {
    console.log('❌ MongoDB Atlas tests failed:', error.message);
  }

  try {
    // Test 4: OpenAI API Connection
    testsTotal++;
    console.log('\n📊 Test 4: OpenAI API Connection');
    
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.data && Array.isArray(data.data)) {
        console.log('✅ OpenAI API connection successful');
        testsPassed++;
      } else {
        console.log('❌ OpenAI API response invalid');
      }
    } else if (response.status === 401) {
      console.log('⚠️ OpenAI API key invalid');
    } else if (response.status === 429) {
      console.log('⚠️ OpenAI API rate limited');
    } else {
      console.log('❌ OpenAI API connection failed');
    }

  } catch (error) {
    console.log('❌ OpenAI API test failed:', error.message);
  }

  try {
    // Test 5: OpenAI Embeddings
    testsTotal++;
    console.log('\n📊 Test 5: OpenAI Embeddings');
    
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: 'Universal AI Brain production test embedding',
        model: 'text-embedding-3-small'
      })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.data && data.data[0] && data.data[0].embedding && Array.isArray(data.data[0].embedding)) {
        console.log('✅ OpenAI embedding generation successful');
        testsPassed++;
      } else {
        console.log('❌ OpenAI embedding response invalid');
      }
    } else if (response.status === 401) {
      console.log('⚠️ OpenAI API key invalid');
    } else if (response.status === 429) {
      console.log('⚠️ OpenAI API rate limited');
    } else {
      console.log('❌ OpenAI embedding generation failed');
    }

  } catch (error) {
    console.log('❌ OpenAI embedding test failed:', error.message);
  }

  // Test 6: Vector Search Pipeline Validation
  testsTotal++;
  console.log('\n📊 Test 6: Vector Search Pipeline Validation');
  
  try {
    const vectorSearchPipeline = [
      {
        $vectorSearch: {
          index: 'memory_vector_index',
          path: 'embedding.values',
          queryVector: Array.from({ length: 1536 }, () => Math.random()),
          numCandidates: 150,
          limit: 10,
          filter: { 'metadata.type': 'test' }
        }
      },
      {
        $addFields: {
          vectorScore: { $meta: 'vectorSearchScore' }
        }
      },
      {
        $match: {
          vectorScore: { $gte: 0.7 }
        }
      }
    ];

    // Validate pipeline structure
    if (vectorSearchPipeline.length === 3 && 
        vectorSearchPipeline[0].$vectorSearch && 
        vectorSearchPipeline[0].$vectorSearch.index === 'memory_vector_index' &&
        vectorSearchPipeline[1].$addFields.vectorScore.$meta === 'vectorSearchScore') {
      console.log('✅ Vector search pipeline syntax validated');
      testsPassed++;
    } else {
      console.log('❌ Vector search pipeline validation failed');
    }
  } catch (error) {
    console.log('❌ Vector search pipeline test failed:', error.message);
  }

  // Test 7: Environment Configuration
  testsTotal++;
  console.log('\n📊 Test 7: Environment Configuration');
  
  try {
    const mongoUriValid = mongoUri && mongoUri.match(/^mongodb(\+srv)?:\/\//);
    const databaseNameValid = databaseName && databaseName.length > 0;
    const openaiKeyValid = openaiApiKey && openaiApiKey.match(/^sk-/);
    
    if (mongoUriValid && databaseNameValid && openaiKeyValid) {
      console.log('✅ Environment configuration validated');
      testsPassed++;
    } else {
      console.log('❌ Environment configuration invalid');
    }
  } catch (error) {
    console.log('❌ Environment configuration test failed:', error.message);
  }

  // Close MongoDB connection
  if (client) {
    await client.close();
    console.log('\n🔌 Disconnected from MongoDB Atlas');
  }

  // Final Results
  console.log('\n' + '='.repeat(60));
  console.log('🎯 UNIVERSAL AI BRAIN PRODUCTION TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${testsPassed}/${testsTotal}`);
  console.log(`📊 Success Rate: ${Math.round((testsPassed / testsTotal) * 100)}%`);
  
  if (testsPassed === testsTotal) {
    console.log('\n🎉 ALL TESTS PASSED - PRODUCTION READY! 🚀');
    console.log('✅ MongoDB Atlas Vector Search: Ready');
    console.log('✅ OpenAI API Integration: Ready');
    console.log('✅ Core Infrastructure: Ready');
    console.log('✅ Universal AI Brain: PRODUCTION READY!');
  } else {
    console.log('\n⚠️ Some tests failed - check configuration');
    if (testsPassed >= testsTotal * 0.8) {
      console.log('🟡 80%+ tests passed - mostly production ready');
    } else {
      console.log('🔴 Less than 80% tests passed - needs attention');
    }
  }
  
  console.log('\n🧠 Universal AI Brain - The Missing 70% for ANY AI Framework');
  console.log('💪 MongoDB Atlas Vector Search + OpenAI = AI Superpowers');
  
  return testsPassed === testsTotal;
}

// Run the tests
testProductionReadiness()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
