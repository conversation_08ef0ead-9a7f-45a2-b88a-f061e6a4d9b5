{"name": "mongodb-ai-agent-boilerplate", "version": "0.1.0", "private": true, "description": "The world's #1 AI Agent Boilerplate, built on MongoDB.", "author": "MongoDB AI", "license": "Apache-2.0", "workspaces": ["packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "test": "turbo run test", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/uuid": "^9.0.8", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "turbo": "^1.12.4", "typescript": "^5.3.3"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.22", "@langchain/core": "^0.1.17", "@langchain/mongodb": "^0.0.1", "@langchain/openai": "^0.0.14", "@mastra/core": "^0.10.6", "@openai/agents": "^0.0.9", "ai": "^4.3.16", "crewai-js": "*", "dotenv": "^16.4.5", "langchain": "^0.1.20", "mongodb": "^6.3.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "packageManager": "npm@10.2.4"}