npm verbose cli C:\Program Files\nodejs\node.exe C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
npm info using npm@10.9.0
npm info using node@v22.11.0
npm info config found workspace root at C:\Users\<USER>\Desktop\boiler\boiler_plate
npm verbose title npm run test
npm verbose argv "run" "test"
npm verbose logfile logs-max:10 dir:C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-06-23T21_04_31_851Z-
npm verbose logfile C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-06-23T21_04_31_851Z-debug-0.log

> @mongodb-ai/core@0.1.0 test
> jest

(node:11484) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:15408) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:15704) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
{"level":"info","time":"2025-06-23T21:04:50.291Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"tavily_web_search","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.290Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.388Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"internal_db_lookup","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.388Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.409Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"text_analysis","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.409Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.434Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"custom_tool","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.434Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.612Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"tavily_web_search","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.612Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.643Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"text_analysis","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.643Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.649Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"text_analysis","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.649Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.672Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"error_tool","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.672Z","message":"Tool executed","msg":"Tool executed"}
{"level":"info","time":"2025-06-23T21:04:50.699Z","service":"mongodb-ai-agent","environment":"test","version":"0.1.0","hostname":"unknown","pid":15408,"trace_id":"631ffb13-f3f8-43ba-809d-471511f27b62","tool_id":"rate_limited_tool","agent_id":"test-agent","workflow_id":"test-workflow","timestamp":"2025-06-23T21:04:50.699Z","message":"Tool executed","msg":"Tool executed"}
FAIL src/__tests__/ToolExecutor.test.ts (11.374 s)
  ● ToolExecutor › built-in tools › should execute tavily_web_search tool

    expect(received).toBeDefined()

    Received: undefined

    [0m [90m 75 |[39m
     [90m 76 |[39m       expect(result)[33m.[39mtoBeDefined()[33m;[39m
    [31m[1m>[22m[39m[90m 77 |[39m       expect(result[33m.[39mresults)[33m.[39mtoBeDefined()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 78 |[39m       expect([33mArray[39m[33m.[39misArray(result[33m.[39mresults))[33m.[39mtoBe([36mtrue[39m)[33m;[39m
     [90m 79 |[39m       expect(result[33m.[39manswer)[33m.[39mtoContain([32m'test search query'[39m)[33m;[39m
     [90m 80 |[39m       expect(result[33m.[39mconfidence)[33m.[39mtoBeGreaterThan([35m0[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:77:30)

  ● ToolExecutor › built-in tools › should execute internal_db_lookup tool

    expect(received).toBeDefined()

    Received: undefined

    [0m [90m 91 |[39m
     [90m 92 |[39m       expect(result)[33m.[39mtoBeDefined()[33m;[39m
    [31m[1m>[22m[39m[90m 93 |[39m       expect(result[33m.[39mresults)[33m.[39mtoBeDefined()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 94 |[39m       expect(result[33m.[39mcount)[33m.[39mtoBe([35m0[39m)[33m;[39m [90m// Mock returns empty results[39m
     [90m 95 |[39m       expect(result[33m.[39mmessage)[33m.[39mtoContain([32m'test_collection'[39m)[33m;[39m
     [90m 96 |[39m     })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:93:30)

  ● ToolExecutor › built-in tools › should execute text_analysis tool

    expect(received).toBe(expected) // Object.is equality

    Expected: "sentiment"
    Received: undefined

    [0m [90m 105 |[39m
     [90m 106 |[39m       expect(result)[33m.[39mtoBeDefined()[33m;[39m
    [31m[1m>[22m[39m[90m 107 |[39m       expect(result[33m.[39manalysis_type)[33m.[39mtoBe([32m'sentiment'[39m)[33m;[39m
     [90m     |[39m                                    [31m[1m^[22m[39m
     [90m 108 |[39m       expect(result[33m.[39msentiment)[33m.[39mtoBe([32m'positive'[39m)[33m;[39m
     [90m 109 |[39m       expect(result[33m.[39mconfidence)[33m.[39mtoBeGreaterThan([35m0[39m)[33m;[39m
     [90m 110 |[39m       expect(result[33m.[39mkey_phrases)[33m.[39mtoBeDefined()[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:107:36)

  ● ToolExecutor › tool registration and execution › should register and execute custom tool

    expect(received).toBe(expected) // Object.is equality

    Expected: "Processed: test data"
    Received: undefined

    [0m [90m 136 |[39m       [36mconst[39m result [33m=[39m [36mawait[39m toolExecutor[33m.[39mexecute([32m'custom_tool'[39m[33m,[39m input[33m,[39m { agent_id[33m:[39m [32m'test-agent'[39m[33m,[39m workflow_id[33m:[39m [32m'test-workflow'[39m[33m,[39m timeout_ms[33m:[39m [35m5000[39m })[33m;[39m
     [90m 137 |[39m
    [31m[1m>[22m[39m[90m 138 |[39m       expect(result[33m.[39mmessage)[33m.[39mtoBe([32m'Processed: test data'[39m)[33m;[39m
     [90m     |[39m                              [31m[1m^[22m[39m
     [90m 139 |[39m       expect(result[33m.[39mtimestamp)[33m.[39mtoBeDefined()[33m;[39m
     [90m 140 |[39m     })[33m;[39m
     [90m 141 |[39m[0m

      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:138:30)

  ● ToolExecutor › tool registration and execution › should throw error for non-existent tool

    expect(received).rejects.toThrow(expected)

    Expected substring: "Tool 'non_existent_tool' not found or inactive"
    Received message:   "Tool with id non_existent_tool not found"

        [0m [90m 36 |[39m     [36mconst[39m tool [33m=[39m [36mawait[39m [36mthis[39m[33m.[39mtoolStore[33m.[39mfindOne({ tool_id[33m:[39m toolId })[33m;[39m
         [90m 37 |[39m     [36mif[39m ([33m![39mtool) {
        [31m[1m>[22m[39m[90m 38 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Tool with id ${toolId} not found`[39m)[33m;[39m
         [90m    |[39m             [31m[1m^[22m[39m
         [90m 39 |[39m     }
         [90m 40 |[39m
         [90m 41 |[39m     [90m// In a real implementation, we would execute the tool here.[39m[0m

      at ToolExecutor.execute (src/agent/ToolExecutor.ts:38:13)
      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:143:7)
      at Object.toThrow (../../node_modules/expect/build/index.js:218:22)
      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:145:17)

  ● ToolExecutor › tool definition management › should create tool definition

    expect(received).toBeDefined()

    Received: undefined

    [0m [90m 174 |[39m       expect(created[33m.[39m_id)[33m.[39mtoBeDefined()[33m;[39m
     [90m 175 |[39m       expect(created[33m.[39mtool_id)[33m.[39mtoBe([32m'test_tool'[39m)[33m;[39m
    [31m[1m>[22m[39m[90m 176 |[39m       expect(created[33m.[39mperformance_stats)[33m.[39mtoBeDefined()[33m;[39m
     [90m     |[39m                                         [31m[1m^[22m[39m
     [90m 177 |[39m       expect(created[33m.[39mperformance_stats[33m![39m[33m.[39mtotal_calls)[33m.[39mtoBe([35m0[39m)[33m;[39m
     [90m 178 |[39m     })[33m;[39m
     [90m 179 |[39m[0m

      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:176:41)

  ● ToolExecutor › execution logging › should log tool execution

    TypeError: Cannot read properties of undefined (reading 'success')

    [0m [90m 217 |[39m       expect(execution[33m.[39mworkflow_id)[33m.[39mtoBe([32m'test-workflow'[39m)[33m;[39m
     [90m 218 |[39m       expect(execution[33m.[39minput)[33m.[39mtoEqual(input)[33m;[39m
    [31m[1m>[22m[39m[90m 219 |[39m       expect(execution[33m.[39mperformance[33m.[39msuccess)[33m.[39mtoBe([36mtrue[39m)[33m;[39m
     [90m     |[39m                                    [31m[1m^[22m[39m
     [90m 220 |[39m       expect(execution[33m.[39mperformance[33m.[39mexecution_time_ms)[33m.[39mtoBeGreaterThan([35m0[39m)[33m;[39m
     [90m 221 |[39m     })[33m;[39m
     [90m 222 |[39m[0m

      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:219:36)

  ● ToolExecutor › error handling › should handle tool execution errors

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: {"result": "Executed Error Tool", "success": true}

    [0m [90m 249 |[39m       })[33m;[39m
     [90m 250 |[39m
    [31m[1m>[22m[39m[90m 251 |[39m       [36mawait[39m expect(
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 252 |[39m         toolExecutor[33m.[39mexecute([32m'error_tool'[39m[33m,[39m {}[33m,[39m { agent_id[33m:[39m [32m'test-agent'[39m[33m,[39m workflow_id[33m:[39m [32m'test-workflow'[39m[33m,[39m timeout_ms[33m:[39m [35m5000[39m })
     [90m 253 |[39m       )[33m.[39mrejects[33m.[39mtoThrow([32m'Tool execution failed'[39m)[33m;[39m
     [90m 254 |[39m[0m

      at expect (../../node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (src/__tests__/ToolExecutor.test.ts:251:13)

FAIL src/__tests__/MongoVectorStore.test.ts (12.662 s)
  ● Console

    console.log
      ✅ MongoDB indexes created successfully

      at MongoVectorStore.ensureIndexes (src/vector/MongoVectorStore.ts:413:15)

    console.log
      ✅ MongoVectorStore initialized successfully

      at MongoVectorStore.initialize (src/vector/MongoVectorStore.ts:104:13)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:181:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:181:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:196:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:196:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:212:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:212:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at MongoVectorStore.hybridSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:256:29)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:236:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at MongoVectorStore.hybridSearch (src/vector/MongoVectorStore.ts:256:29)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:236:23)

    console.error
      Error in hybrid search: Error: Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:240:13)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at MongoVectorStore.hybridSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:256:29)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:236:23)

    [0m [90m 296 |[39m         [33m.[39mslice([35m0[39m[33m,[39m options[33m.[39mlimit [33m||[39m [35m10[39m)[33m;[39m
     [90m 297 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 298 |[39m       console[33m.[39merror([32m'Error in hybrid search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 299 |[39m       [90m// Fallback to vector search only[39m
     [90m 300 |[39m       [36mreturn[39m [36mthis[39m[33m.[39mvectorSearch(query[33m,[39m options)[33m;[39m
     [90m 301 |[39m     }[0m

      at MongoVectorStore.hybridSearch (src/vector/MongoVectorStore.ts:298:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:236:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:236:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:236:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at MongoVectorStore.hybridSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:256:29)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:252:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at MongoVectorStore.hybridSearch (src/vector/MongoVectorStore.ts:256:29)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:252:23)

    console.error
      Error in hybrid search: Error: Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:240:13)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at MongoVectorStore.hybridSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:256:29)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:252:23)

    [0m [90m 296 |[39m         [33m.[39mslice([35m0[39m[33m,[39m options[33m.[39mlimit [33m||[39m [35m10[39m)[33m;[39m
     [90m 297 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 298 |[39m       console[33m.[39merror([32m'Error in hybrid search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 299 |[39m       [90m// Fallback to vector search only[39m
     [90m 300 |[39m       [36mreturn[39m [36mthis[39m[33m.[39mvectorSearch(query[33m,[39m options)[33m;[39m
     [90m 301 |[39m     }[0m

      at MongoVectorStore.hybridSearch (src/vector/MongoVectorStore.ts:298:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:252:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:252:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:252:23)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:277:27) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:277:27)

    console.error
      Error getting vector store stats: TypeError: this.collection.stats is not a function
          at MongoVectorStore.getStats (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:458:25)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:319:39)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 470 |[39m       }[33m;[39m
     [90m 471 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 472 |[39m       console[33m.[39merror([32m'Error getting vector store stats:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 473 |[39m       [36mreturn[39m { error[33m:[39m error[33m.[39mmessage }[33m;[39m
     [90m 474 |[39m     }
     [90m 475 |[39m   }[0m

      at MongoVectorStore.getStats (src/vector/MongoVectorStore.ts:472:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:319:39)

    console.error
      Error getting vector store stats: TypeError: this.collection.stats is not a function
          at MongoVectorStore.getStats (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:458:25)
          at MongoVectorStore.healthCheck (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:593:32)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:329:40)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 470 |[39m       }[33m;[39m
     [90m 471 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 472 |[39m       console[33m.[39merror([32m'Error getting vector store stats:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 473 |[39m       [36mreturn[39m { error[33m:[39m error[33m.[39mmessage }[33m;[39m
     [90m 474 |[39m     }
     [90m 475 |[39m   }[0m

      at MongoVectorStore.getStats (src/vector/MongoVectorStore.ts:472:15)
      at MongoVectorStore.healthCheck (src/vector/MongoVectorStore.ts:593:32)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:329:40)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at MongoVectorStore.healthCheck (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:594:25)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:329:22) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at MongoVectorStore.healthCheck (src/vector/MongoVectorStore.ts:594:25)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:329:22)

    console.error
      Error getting document: BSONError: input must be a 24 character hex string, 12 byte Uint8Array, or an integer
          at new ObjectId (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\bson\src\objectid.ts:120:15)
          at MongoVectorStore.getDocument (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:523:16)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:354:44)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 525 |[39m       )[33m;[39m
     [90m 526 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 527 |[39m       console[33m.[39merror([32m'Error getting document:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 528 |[39m       [36mreturn[39m [36mnull[39m[33m;[39m
     [90m 529 |[39m     }
     [90m 530 |[39m   }[0m

      at MongoVectorStore.getDocument (src/vector/MongoVectorStore.ts:527:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:354:44)

    console.error
      Error in vector search: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at MongoVectorStore.vectorSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\vector\MongoVectorStore.ts:236:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\MongoVectorStore.test.ts:360:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 237 |[39m       [36mreturn[39m results[33m;[39m
     [90m 238 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:239:15)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:360:23)

  ● MongoVectorStore › Vector Search › should perform vector search with text query

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:181:23)

  ● MongoVectorStore › Vector Search › should perform vector search with embedding array

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:196:23)

  ● MongoVectorStore › Vector Search › should apply filters in vector search

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:212:23)

  ● MongoVectorStore › Hybrid Search › should perform hybrid search combining vector and text

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:236:23)

  ● MongoVectorStore › Hybrid Search › should fallback to vector search if text search fails

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:252:23)

  ● MongoVectorStore › Document Management › should find similar documents

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:277:27)

  ● MongoVectorStore › Health and Statistics › should provide vector store statistics

    expect(received).toHaveProperty(path)

    Expected path: "documentCount"
    Received path: []

    Received value: {"error": "this.collection.stats is not a function"}

    [0m [90m 320 |[39m       
     [90m 321 |[39m       expect(stats)[33m.[39mtoBeDefined()[33m;[39m
    [31m[1m>[22m[39m[90m 322 |[39m       expect(stats)[33m.[39mtoHaveProperty([32m'documentCount'[39m)[33m;[39m
     [90m     |[39m                     [31m[1m^[22m[39m
     [90m 323 |[39m       expect(stats)[33m.[39mtoHaveProperty([32m'isInitialized'[39m)[33m;[39m
     [90m 324 |[39m       expect(stats)[33m.[39mtoHaveProperty([32m'embeddingProvider'[39m)[33m;[39m
     [90m 325 |[39m       expect(stats[33m.[39misInitialized)[33m.[39mtoBe([36mtrue[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:322:21)

  ● MongoVectorStore › Error Handling › should handle search errors gracefully

    Vector search failed: $vectorSearch stage is only allowed on MongoDB Atlas

    [0m [90m 238 |[39m     } [36mcatch[39m (error) {
     [90m 239 |[39m       console[33m.[39merror([32m'Error in vector search:'[39m[33m,[39m error)[33m;[39m
    [31m[1m>[22m[39m[90m 240 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Vector search failed: ${error.message}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 241 |[39m     }
     [90m 242 |[39m   }
     [90m 243 |[39m[0m

      at MongoVectorStore.vectorSearch (src/vector/MongoVectorStore.ts:240:13)
      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:360:23)

  ● MongoVectorStore › Error Handling › should require initialization before use

    ReferenceError: mongoConnection is not defined

    [0m [90m 365 |[39m     it([32m'should require initialization before use'[39m[33m,[39m () [33m=>[39m {
     [90m 366 |[39m       [36mconst[39m uninitializedStore [33m=[39m [36mnew[39m [33mMongoVectorStore[39m(
    [31m[1m>[22m[39m[90m 367 |[39m         mongoConnection[33m,[39m
     [90m     |[39m         [31m[1m^[22m[39m
     [90m 368 |[39m         [32m'uninitialized_collection'[39m
     [90m 369 |[39m       )[33m;[39m
     [90m 370 |[39m[0m

      at Object.<anonymous> (src/__tests__/MongoVectorStore.test.ts:367:9)

FAIL src/__tests__/WorkflowEngine.test.ts (13.858 s)
  ● WorkflowEngine › workflow execution › should execute a simple workflow

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:165:7)

  ● WorkflowEngine › workflow execution › should execute workflow with multiple steps

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:195:7)

  ● WorkflowEngine › workflow execution › should handle workflow step dependencies

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:225:7)

  ● WorkflowEngine › workflow execution › should skip steps with unmet dependencies

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:258:7)

  ● WorkflowEngine › workflow error handling › should handle step failures

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:283:7)

  ● WorkflowEngine › workflow error handling › should retry failed steps

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:307:7)

  ● WorkflowEngine › workflow management › should cancel a workflow

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.cancelWorkflow (src/agent/WorkflowEngine.ts:469:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:329:7)

  ● WorkflowEngine › workflow management › should get workflow summary

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:355:7)

  ● WorkflowEngine › conditional execution › should execute steps based on conditions

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:389:7)

  ● WorkflowEngine › conditional execution › should skip steps when conditions are not met

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at WorkflowEngine.updateWorkflowStatus (src/agent/WorkflowEngine.ts:409:32)
      at WorkflowEngine.executeWorkflow (src/agent/WorkflowEngine.ts:142:5)
      at Object.<anonymous> (src/__tests__/WorkflowEngine.test.ts:417:7)

FAIL src/__tests__/HybridSearch.test.ts (5.702 s)
  ● Console

    console.warn
      Using fallback mock embedding provider for: test query...

    [0m [90m 11 |[39m [36mexport[39m [36mclass[39m [33mDefaultEmbeddingProvider[39m [36mimplements[39m [33mEmbeddingProvider[39m {
     [90m 12 |[39m   [36masync[39m generateEmbedding(text[33m:[39m string)[33m:[39m [33mPromise[39m[33m<[39m[33mnumber[39m[][33m>[39m {
    [31m[1m>[22m[39m[90m 13 |[39m     console[33m.[39mwarn([32m`Using fallback mock embedding provider for: ${text.substring(0, 50)}...`[39m)[33m;[39m
     [90m    |[39m             [31m[1m^[22m[39m
     [90m 14 |[39m     console[33m.[39mwarn([32m'WARNING: This is a mock implementation. For production, configure a real embedding provider.'[39m)[33m;[39m
     [90m 15 |[39m     [90m// Mock implementation - generates consistent but meaningless embeddings[39m
     [90m 16 |[39m     [36mreturn[39m [33mArray[39m([35m1536[39m)[33m.[39mfill([35m0[39m)[33m.[39mmap(() [33m=>[39m [33mMath[39m[33m.[39mrandom() [33m*[39m [35m2[39m [33m-[39m [35m1[39m)[33m;[39m [90m// Random values between -1 and 1[39m[0m

      at DefaultEmbeddingProvider.generateEmbedding (src/features/hybridSearch.ts:13:13)
      at HybridSearchEngine.search (src/features/hybridSearch.ts:98:59)
      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:219:42)

    console.warn
      WARNING: This is a mock implementation. For production, configure a real embedding provider.

    [0m [90m 12 |[39m   [36masync[39m generateEmbedding(text[33m:[39m string)[33m:[39m [33mPromise[39m[33m<[39m[33mnumber[39m[][33m>[39m {
     [90m 13 |[39m     console[33m.[39mwarn([32m`Using fallback mock embedding provider for: ${text.substring(0, 50)}...`[39m)[33m;[39m
    [31m[1m>[22m[39m[90m 14 |[39m     console[33m.[39mwarn([32m'WARNING: This is a mock implementation. For production, configure a real embedding provider.'[39m)[33m;[39m
     [90m    |[39m             [31m[1m^[22m[39m
     [90m 15 |[39m     [90m// Mock implementation - generates consistent but meaningless embeddings[39m
     [90m 16 |[39m     [36mreturn[39m [33mArray[39m([35m1536[39m)[33m.[39mfill([35m0[39m)[33m.[39mmap(() [33m=>[39m [33mMath[39m[33m.[39mrandom() [33m*[39m [35m2[39m [33m-[39m [35m1[39m)[33m;[39m [90m// Random values between -1 and 1[39m
     [90m 17 |[39m   }[0m

      at DefaultEmbeddingProvider.generateEmbedding (src/features/hybridSearch.ts:14:13)
      at HybridSearchEngine.search (src/features/hybridSearch.ts:98:59)
      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:219:42)

    console.error
      Hybrid search failed: MongoServerError: $vectorSearch stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at HybridSearchEngine.executeHybridSearchPipeline (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\features\hybridSearch.ts:216:21)
          at HybridSearchEngine.search (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\features\hybridSearch.ts:104:23)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\HybridSearch.test.ts:219:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$vectorSearch stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 119 |[39m       [36mreturn[39m results[33m;[39m
     [90m 120 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 121 |[39m       console[33m.[39merror([32m'Hybrid search failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 122 |[39m       [90m// Fallback to text-only search[39m
     [90m 123 |[39m       [36mreturn[39m [36mawait[39m [36mthis[39m[33m.[39mfallbackTextSearch(query[33m,[39m filters[33m,[39m options)[33m;[39m
     [90m 124 |[39m     }[0m

      at HybridSearchEngine.search (src/features/hybridSearch.ts:121:15)
      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:219:23)

    console.log
      Falling back to text-only search

      at HybridSearchEngine.fallbackTextSearch (src/features/hybridSearch.ts:240:13)

    console.error
      Text search also failed: MongoServerError: $search stage is only allowed on MongoDB Atlas
          at Connection.sendCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:559:17)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Connection.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cmap\connection.ts:633:22)
          at Server.command (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\sdam\server.ts:342:21)
          at AggregateOperation.executeCommand (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\command.ts:179:12)
          at AggregateOperation.execute (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\aggregate.ts:155:12)
          at tryOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
          at executeOperation (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
          at AggregationCursor._initialize (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\aggregation_cursor.ts:92:22)
          at AggregationCursor.cursorInit (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:944:21)
          at AggregationCursor.fetchBatch (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:982:7)
          at AggregationCursor.next (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:612:9)
          at AggregationCursor.[Symbol.asyncIterator] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:506:26)
          at AggregationCursor.toArray (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\mongodb\src\cursor\abstract_cursor.ts:700:22)
          at HybridSearchEngine.fallbackTextSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\features\hybridSearch.ts:281:23)
          at HybridSearchEngine.search (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\features\hybridSearch.ts:123:14)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\HybridSearch.test.ts:219:23) {
        errorLabelSet: Set(0) {},
        errorResponse: {
          ok: 0,
          errmsg: '$search stage is only allowed on MongoDB Atlas',
          code: 6047401,
          codeName: 'Location6047401'
        },
        ok: 0,
        code: 6047401,
        codeName: 'Location6047401'
      }

    [0m [90m 294 |[39m       }))[33m;[39m
     [90m 295 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 296 |[39m       console[33m.[39merror([32m'Text search also failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 297 |[39m       [36mreturn[39m [][33m;[39m
     [90m 298 |[39m     }
     [90m 299 |[39m   }[0m

      at HybridSearchEngine.fallbackTextSearch (src/features/hybridSearch.ts:296:15)
      at HybridSearchEngine.search (src/features/hybridSearch.ts:123:14)
      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:219:23)

  ● HybridSearchEngine › vector embedding operations › should store and retrieve vector embeddings

    TypeError: embeddingProvider.upsert is not a function

    [0m [90m 56 |[39m       ][33m;[39m
     [90m 57 |[39m
    [31m[1m>[22m[39m[90m 58 |[39m       [36mawait[39m embeddingProvider[33m.[39mupsert(testVectors)[33m;[39m
     [90m    |[39m                               [31m[1m^[22m[39m
     [90m 59 |[39m
     [90m 60 |[39m       [90m// Verify vectors were stored[39m
     [90m 61 |[39m       [36mconst[39m collection [33m=[39m getTestDb()[33m.[39mcollection([32m'vector_embeddings'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:58:31)

  ● HybridSearchEngine › semantic search › should perform semantic search

    TypeError: embeddingProvider.upsert is not a function

    [0m [90m 116 |[39m       ][33m;[39m
     [90m 117 |[39m
    [31m[1m>[22m[39m[90m 118 |[39m       [36mawait[39m embeddingProvider[33m.[39mupsert(testVectors)[33m;[39m
     [90m     |[39m                               [31m[1m^[22m[39m
     [90m 119 |[39m     })[33m;[39m
     [90m 120 |[39m
     [90m 121 |[39m     it([32m'should perform semantic search'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:118:31)

  ● HybridSearchEngine › semantic search › should filter search results

    TypeError: embeddingProvider.upsert is not a function

    [0m [90m 116 |[39m       ][33m;[39m
     [90m 117 |[39m
    [31m[1m>[22m[39m[90m 118 |[39m       [36mawait[39m embeddingProvider[33m.[39mupsert(testVectors)[33m;[39m
     [90m     |[39m                               [31m[1m^[22m[39m
     [90m 119 |[39m     })[33m;[39m
     [90m 120 |[39m
     [90m 121 |[39m     it([32m'should perform semantic search'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:118:31)

  ● HybridSearchEngine › hybrid search › should perform hybrid search with default weights

    TypeError: embeddingProvider.upsert is not a function

    [0m [90m 170 |[39m       ][33m;[39m
     [90m 171 |[39m
    [31m[1m>[22m[39m[90m 172 |[39m       [36mawait[39m embeddingProvider[33m.[39mupsert(testVectors)[33m;[39m
     [90m     |[39m                               [31m[1m^[22m[39m
     [90m 173 |[39m     })[33m;[39m
     [90m 174 |[39m
     [90m 175 |[39m     it([32m'should perform hybrid search with default weights'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:172:31)

  ● HybridSearchEngine › hybrid search › should include relevance explanations

    TypeError: embeddingProvider.upsert is not a function

    [0m [90m 170 |[39m       ][33m;[39m
     [90m 171 |[39m
    [31m[1m>[22m[39m[90m 172 |[39m       [36mawait[39m embeddingProvider[33m.[39mupsert(testVectors)[33m;[39m
     [90m     |[39m                               [31m[1m^[22m[39m
     [90m 173 |[39m     })[33m;[39m
     [90m 174 |[39m
     [90m 175 |[39m     it([32m'should perform hybrid search with default weights'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/HybridSearch.test.ts:172:31)

FAIL src/__tests__/MongoMemoryProvider.test.ts (5.692 s)
  ● MongoMemoryProvider › working state management › should update and retrieve working state

    TypeError: memoryProvider.updateWorkingState is not a function

    [0m [90m 76 |[39m       }[33m;[39m
     [90m 77 |[39m
    [31m[1m>[22m[39m[90m 78 |[39m       [36mawait[39m memoryProvider[33m.[39mupdateWorkingState(agentId[33m,[39m sessionId[33m,[39m workingState)[33m;[39m
     [90m    |[39m                            [31m[1m^[22m[39m
     [90m 79 |[39m       [36mconst[39m retrieved [33m=[39m [36mawait[39m memoryProvider[33m.[39mgetWorkingState(agentId[33m,[39m sessionId)[33m;[39m
     [90m 80 |[39m
     [90m 81 |[39m       expect(retrieved)[33m.[39mtoEqual(workingState)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoMemoryProvider.test.ts:78:28)

  ● MongoMemoryProvider › working state management › should return null for non-existent working state

    TypeError: memoryProvider.getWorkingState is not a function

    [0m [90m 83 |[39m
     [90m 84 |[39m     it([32m'should return null for non-existent working state'[39m[33m,[39m [36masync[39m () [33m=>[39m {
    [31m[1m>[22m[39m[90m 85 |[39m       [36mconst[39m state [33m=[39m [36mawait[39m memoryProvider[33m.[39mgetWorkingState([32m'non-existent'[39m[33m,[39m [32m'non-existent'[39m)[33m;[39m
     [90m    |[39m                                          [31m[1m^[22m[39m
     [90m 86 |[39m       expect(state)[33m.[39mtoBeNull()[33m;[39m
     [90m 87 |[39m     })[33m;[39m
     [90m 88 |[39m   })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoMemoryProvider.test.ts:85:42)

  ● MongoMemoryProvider › temporary findings management › should update and retrieve temporary findings

    TypeError: memoryProvider.updateTempFindings is not a function

    [0m [90m  96 |[39m       }[33m;[39m
     [90m  97 |[39m
    [31m[1m>[22m[39m[90m  98 |[39m       [36mawait[39m memoryProvider[33m.[39mupdateTempFindings(agentId[33m,[39m sessionId[33m,[39m findings)[33m;[39m
     [90m     |[39m                            [31m[1m^[22m[39m
     [90m  99 |[39m       [36mconst[39m retrieved [33m=[39m [36mawait[39m memoryProvider[33m.[39mgetTempFindings(agentId[33m,[39m sessionId)[33m;[39m
     [90m 100 |[39m
     [90m 101 |[39m       expect(retrieved)[33m.[39mtoEqual(findings)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoMemoryProvider.test.ts:98:28)

  ● MongoMemoryProvider › temporary findings management › should return null for non-existent findings

    TypeError: memoryProvider.getTempFindings is not a function

    [0m [90m 103 |[39m
     [90m 104 |[39m     it([32m'should return null for non-existent findings'[39m[33m,[39m [36masync[39m () [33m=>[39m {
    [31m[1m>[22m[39m[90m 105 |[39m       [36mconst[39m findings [33m=[39m [36mawait[39m memoryProvider[33m.[39mgetTempFindings([32m'non-existent'[39m[33m,[39m [32m'non-existent'[39m)[33m;[39m
     [90m     |[39m                                             [31m[1m^[22m[39m
     [90m 106 |[39m       expect(findings)[33m.[39mtoBeNull()[33m;[39m
     [90m 107 |[39m     })[33m;[39m
     [90m 108 |[39m   })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoMemoryProvider.test.ts:105:45)

  ● MongoMemoryProvider › session management › should get active sessions for an agent

    TypeError: memoryProvider.getActiveSessions is not a function

    [0m [90m 143 |[39m       [36mawait[39m memoryProvider[33m.[39maddMessage(agentId[33m,[39m session2[33m,[39m message)[33m;[39m
     [90m 144 |[39m
    [31m[1m>[22m[39m[90m 145 |[39m       [36mconst[39m sessions [33m=[39m [36mawait[39m memoryProvider[33m.[39mgetActiveSessions(agentId)[33m;[39m
     [90m     |[39m                                             [31m[1m^[22m[39m
     [90m 146 |[39m       
     [90m 147 |[39m       expect(sessions)[33m.[39mtoHaveLength([35m2[39m)[33m;[39m
     [90m 148 |[39m       expect(sessions)[33m.[39mtoContain(session1)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoMemoryProvider.test.ts:145:45)

FAIL src/__tests__/MongoDataStore.test.ts
  ● MongoDataStore › findById › should find a document by ID

    TypeError: dataStore.findById is not a function

    [0m [90m 42 |[39m       [36mconst[39m created [33m=[39m [36mawait[39m dataStore[33m.[39mcreate(testDoc)[33m;[39m
     [90m 43 |[39m       
    [31m[1m>[22m[39m[90m 44 |[39m       [36mconst[39m found [33m=[39m [36mawait[39m dataStore[33m.[39mfindById(created[33m.[39m_id[33m![39m[33m.[39mtoString())[33m;[39m
     [90m    |[39m                                     [31m[1m^[22m[39m
     [90m 45 |[39m       
     [90m 46 |[39m       expect(found)[33m.[39mtoBeTruthy()[33m;[39m
     [90m 47 |[39m       expect(found[33m![39m[33m.[39mname)[33m.[39mtoBe([32m'test'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoDataStore.test.ts:44:37)

  ● MongoDataStore › findById › should return null for non-existent ID

    TypeError: dataStore.findById is not a function

    [0m [90m 51 |[39m     it([32m'should return null for non-existent ID'[39m[33m,[39m [36masync[39m () [33m=>[39m {
     [90m 52 |[39m       [36mconst[39m nonExistentId [33m=[39m [36mnew[39m [33mObjectId[39m()[33m.[39mtoString()[33m;[39m
    [31m[1m>[22m[39m[90m 53 |[39m       [36mconst[39m result [33m=[39m [36mawait[39m dataStore[33m.[39mfindById(nonExistentId)[33m;[39m
     [90m    |[39m                                      [31m[1m^[22m[39m
     [90m 54 |[39m       
     [90m 55 |[39m       expect(result)[33m.[39mtoBeNull()[33m;[39m
     [90m 56 |[39m     })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/MongoDataStore.test.ts:53:38)

  ● MongoDataStore › update › should update a document

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at Object.<anonymous> (src/__tests__/MongoDataStore.test.ts:82:39)

  ● MongoDataStore › update › should return null for non-existent ID

    MongoInvalidArgumentError: Update document requires atomic operators

    [0m [90m 30 |[39m
     [90m 31 |[39m   [36masync[39m update(id[33m:[39m string[33m,[39m item[33m:[39m [33mUpdateFilter[39m[33m<[39m[33mT[39m[33m>[39m)[33m:[39m [33mPromise[39m[33m<[39m[33mT[39m [33m|[39m [36mnull[39m[33m>[39m {
    [31m[1m>[22m[39m[90m 32 |[39m     [36mawait[39m [36mthis[39m[33m.[39mcollection[33m.[39mupdateOne({ _id[33m:[39m [36mnew[39m [33mObjectId[39m(id) } [36mas[39m any[33m,[39m item)[33m;[39m
     [90m    |[39m                           [31m[1m^[22m[39m
     [90m 33 |[39m     [36mreturn[39m [36mthis[39m[33m.[39mread(id)[33m;[39m
     [90m 34 |[39m   }
     [90m 35 |[39m[0m

      at new UpdateOneOperation (../../node_modules/mongodb/src/operations/update.ts:148:13)
      at Collection.updateOne (../../node_modules/mongodb/src/collection.ts:373:7)
      at MongoDataStore.update (src/persistance/MongoDataStore.ts:32:27)
      at Object.<anonymous> (src/__tests__/MongoDataStore.test.ts:91:38)

  ● MongoDataStore › delete › should delete a document

    TypeError: dataStore.findById is not a function

    [0m [90m 105 |[39m       
     [90m 106 |[39m       [90m// Verify it's actually deleted[39m
    [31m[1m>[22m[39m[90m 107 |[39m       [36mconst[39m found [33m=[39m [36mawait[39m dataStore[33m.[39mfindById(created[33m.[39m_id[33m![39m[33m.[39mtoString())[33m;[39m
     [90m     |[39m                                     [31m[1m^[22m[39m
     [90m 108 |[39m       expect(found)[33m.[39mtoBeNull()[33m;[39m
     [90m 109 |[39m     })[33m;[39m
     [90m 110 |[39m[0m

      at Object.<anonymous> (src/__tests__/MongoDataStore.test.ts:107:37)

FAIL src/__tests__/intelligence/ContextInjectionEngine.test.ts
  ● Console

    console.error
      Context injection failed: Error: Memory error
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\ContextInjectionEngine.test.ts:95:75)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 182 |[39m       }[33m;[39m
     [90m 183 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 184 |[39m       console[33m.[39merror([32m'Context injection failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 185 |[39m       
     [90m 186 |[39m       [90m// Return original prompt with minimal context on failure[39m
     [90m 187 |[39m       [36mreturn[39m {[0m

      at ContextInjectionEngine.enhancePrompt (src/intelligence/ContextInjectionEngine.ts:184:15)
      at Object.<anonymous> (src/__tests__/intelligence/ContextInjectionEngine.test.ts:99:24)

  ● ContextInjectionEngine › Context Selection › should prioritize recent context when enabled

    expect(received).toBe(expected) // Object.is equality

    Expected: "memory_recent"
    Received: "memory_old"

    [0m [90m 218 |[39m
     [90m 219 |[39m       [90m// Recent memory should be prioritized despite lower importance[39m
    [31m[1m>[22m[39m[90m 220 |[39m       expect(context[[35m0[39m][33m.[39mid)[33m.[39mtoBe([32m'memory_recent'[39m)[33m;[39m
     [90m     |[39m                             [31m[1m^[22m[39m
     [90m 221 |[39m     })[33m;[39m
     [90m 222 |[39m
     [90m 223 |[39m     it([32m'should filter by minimum relevance score'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/intelligence/ContextInjectionEngine.test.ts:220:29)

  ● ContextInjectionEngine › Framework-Specific Optimization › should optimize context for Vercel AI

    expect(received).toBeLessThan(expected)

    Expected: < 158
    Received:   158

    [0m [90m 294 |[39m       )[33m;[39m
     [90m 295 |[39m
    [31m[1m>[22m[39m[90m 296 |[39m       expect(optimized[[35m0[39m][33m.[39mcontent[33m.[39mlength)[33m.[39mtoBeLessThan(contextItems[[35m0[39m][33m.[39mcontent[33m.[39mlength)[33m;[39m
     [90m     |[39m                                           [31m[1m^[22m[39m
     [90m 297 |[39m     })[33m;[39m
     [90m 298 |[39m
     [90m 299 |[39m     it([32m'should optimize context for Mastra'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/intelligence/ContextInjectionEngine.test.ts:296:43)

FAIL src/__tests__/SchemaValidator.test.ts
  ● SchemaValidator › agent schema validation › should validate a correct agent document

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 15 |[39m       }[33m;[39m
     [90m 16 |[39m
    [31m[1m>[22m[39m[90m 17 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'agent'[39m[33m,[39m validAgent)[33m;[39m
     [90m    |[39m                                      [31m[1m^[22m[39m
     [90m 18 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mtrue[39m)[33m;[39m
     [90m 19 |[39m       expect(result[33m.[39merrors)[33m.[39mtoBeUndefined()[33m;[39m
     [90m 20 |[39m     })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:17:38)

  ● SchemaValidator › agent schema validation › should reject agent with missing required fields

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 26 |[39m       }[33m;[39m
     [90m 27 |[39m
    [31m[1m>[22m[39m[90m 28 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'agent'[39m[33m,[39m invalidAgent)[33m;[39m
     [90m    |[39m                                      [31m[1m^[22m[39m
     [90m 29 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mfalse[39m)[33m;[39m
     [90m 30 |[39m       expect(result[33m.[39merrors)[33m.[39mtoBeDefined()[33m;[39m
     [90m 31 |[39m       expect(result[33m.[39merrors[33m![39m[33m.[39mlength)[33m.[39mtoBeGreaterThan([35m0[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:28:38)

  ● SchemaValidator › agent schema validation › should reject agent with invalid status

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 44 |[39m       }[33m;[39m
     [90m 45 |[39m
    [31m[1m>[22m[39m[90m 46 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'agent'[39m[33m,[39m invalidAgent)[33m;[39m
     [90m    |[39m                                      [31m[1m^[22m[39m
     [90m 47 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mfalse[39m)[33m;[39m
     [90m 48 |[39m       expect(result[33m.[39merrors)[33m.[39mtoBeDefined()[33m;[39m
     [90m 49 |[39m     })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:46:38)

  ● SchemaValidator › agent working memory schema validation › should validate a correct working memory document

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 66 |[39m       }[33m;[39m
     [90m 67 |[39m
    [31m[1m>[22m[39m[90m 68 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'agentWorkingMemory'[39m[33m,[39m validWorkingMemory)[33m;[39m
     [90m    |[39m                                      [31m[1m^[22m[39m
     [90m 69 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mtrue[39m)[33m;[39m
     [90m 70 |[39m     })[33m;[39m
     [90m 71 |[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:68:38)

  ● SchemaValidator › agent working memory schema validation › should reject working memory with invalid role

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 85 |[39m       }[33m;[39m
     [90m 86 |[39m
    [31m[1m>[22m[39m[90m 87 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'agentWorkingMemory'[39m[33m,[39m invalidWorkingMemory)[33m;[39m
     [90m    |[39m                                      [31m[1m^[22m[39m
     [90m 88 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mfalse[39m)[33m;[39m
     [90m 89 |[39m     })[33m;[39m
     [90m 90 |[39m   })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:87:38)

  ● SchemaValidator › vector embeddings schema validation › should validate a correct vector embedding document

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 109 |[39m       }[33m;[39m
     [90m 110 |[39m
    [31m[1m>[22m[39m[90m 111 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'vectorEmbeddings'[39m[33m,[39m validEmbedding)[33m;[39m
     [90m     |[39m                                      [31m[1m^[22m[39m
     [90m 112 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mtrue[39m)[33m;[39m
     [90m 113 |[39m     })[33m;[39m
     [90m 114 |[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:111:38)

  ● SchemaValidator › vector embeddings schema validation › should reject embedding with missing content

    TypeError: validator_1.SchemaValidator.validate is not a function

    [0m [90m 129 |[39m       }[33m;[39m
     [90m 130 |[39m
    [31m[1m>[22m[39m[90m 131 |[39m       [36mconst[39m result [33m=[39m [33mSchemaValidator[39m[33m.[39mvalidate([32m'vectorEmbeddings'[39m[33m,[39m invalidEmbedding)[33m;[39m
     [90m     |[39m                                      [31m[1m^[22m[39m
     [90m 132 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mfalse[39m)[33m;[39m
     [90m 133 |[39m     })[33m;[39m
     [90m 134 |[39m   })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:131:38)

  ● SchemaValidator › validateOrThrow › should not throw for valid data

    expect(received).not.toThrow()

    Error name:    "Error"
    Error message: "Schema validation failed for agent:  must have required property 'created_at',  must have required property 'updated_at'"

        [0m [90m 18 |[39m     [36mif[39m ([33m![39mvalid) {
         [90m 19 |[39m       [36mconst[39m errorMessages [33m=[39m validate[33m.[39merrors[33m?[39m[33m.[39mmap(error [33m=>[39m [32m`${error.instancePath} ${error.message}`[39m)[33m.[39mjoin([32m', '[39m)[33m;[39m
        [31m[1m>[22m[39m[90m 20 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Schema validation failed for ${schemaName}: ${errorMessages}`[39m)[33m;[39m
         [90m    |[39m             [31m[1m^[22m[39m
         [90m 21 |[39m     }
         [90m 22 |[39m   }
         [90m 23 |[39m }[0m

      at Function.validateOrThrow (src/schemas/validator.ts:20:13)
      at src/__tests__/SchemaValidator.test.ts:150:25
      at Object.<anonymous> (../../node_modules/expect/build/toThrowMatchers.js:74:11)
      at Object.throwingMatcher [as toThrow] (../../node_modules/expect/build/index.js:320:21)
      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:151:14)
      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:151:14)

  ● SchemaValidator › getAvailableSchemas › should return list of available schemas

    TypeError: validator_1.SchemaValidator.getAvailableSchemas is not a function

    [0m [90m 166 |[39m   describe([32m'getAvailableSchemas'[39m[33m,[39m () [33m=>[39m {
     [90m 167 |[39m     it([32m'should return list of available schemas'[39m[33m,[39m () [33m=>[39m {
    [31m[1m>[22m[39m[90m 168 |[39m       [36mconst[39m schemas [33m=[39m [33mSchemaValidator[39m[33m.[39mgetAvailableSchemas()[33m;[39m
     [90m     |[39m                                       [31m[1m^[22m[39m
     [90m 169 |[39m       
     [90m 170 |[39m       expect(schemas)[33m.[39mtoContain([32m'agent'[39m)[33m;[39m
     [90m 171 |[39m       expect(schemas)[33m.[39mtoContain([32m'agentWorkingMemory'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/SchemaValidator.test.ts:168:39)

PASS src/__tests__/collections/ContextCollection.test.ts
  ● Console

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection custom_context

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:499:39)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

    console.warn
      ⚠️ No schema found for collection context_items

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ContextCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ContextCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ContextCollection (src/collections/ContextCollection.ts:95:10)
      at Object.<anonymous> (src/__tests__/collections/ContextCollection.test.ts:38:25)

FAIL src/__tests__/PerformanceBenchmarks.test.ts
  ● Test suite failed to run

    Cannot find module '../brain/UniversalAIBrain' from 'src/__tests__/PerformanceBenchmarks.test.ts'

    [0m [90m  8 |[39m
     [90m  9 |[39m [36mimport[39m { setupTestDb[33m,[39m teardownTestDb } [36mfrom[39m [32m'./setup'[39m[33m;[39m
    [31m[1m>[22m[39m[90m 10 |[39m [36mimport[39m { [33mUniversalAIBrain[39m[33m,[39m [33mBrainConfig[39m } [36mfrom[39m [32m'../brain/UniversalAIBrain'[39m[33m;[39m
     [90m    |[39m [31m[1m^[22m[39m
     [90m 11 |[39m [36mimport[39m { [33mFrameworkAdapterManager[39m } [36mfrom[39m [32m'../adapters/FrameworkAdapterManager'[39m[33m;[39m
     [90m 12 |[39m [36mimport[39m { [33mVercelAIAdapter[39m } [36mfrom[39m [32m'../adapters/VercelAIAdapter'[39m[33m;[39m
     [90m 13 |[39m [36mimport[39m { [33mMastraAdapter[39m } [36mfrom[39m [32m'../adapters/MastraAdapter'[39m[33m;[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/PerformanceBenchmarks.test.ts:10:1)

FAIL src/__tests__/UniversalAIBrain.integration.test.ts
  ● Test suite failed to run

    Cannot find module '../brain/UniversalAIBrain' from 'src/__tests__/UniversalAIBrain.integration.test.ts'

    [0m [90m 4 |[39m [90m */[39m
     [90m 5 |[39m
    [31m[1m>[22m[39m[90m 6 |[39m [36mimport[39m { [33mUniversalAIBrain[39m } [36mfrom[39m [32m'../brain/UniversalAIBrain'[39m[33m;[39m
     [90m   |[39m [31m[1m^[22m[39m
     [90m 7 |[39m [36mimport[39m { [33mSemanticMemoryEngine[39m } [36mfrom[39m [32m'../intelligence/SemanticMemoryEngine'[39m[33m;[39m
     [90m 8 |[39m [36mimport[39m { [33mContextInjectionEngine[39m } [36mfrom[39m [32m'../intelligence/ContextInjectionEngine'[39m[33m;[39m
     [90m 9 |[39m [36mimport[39m { [33mVectorSearchEngine[39m } [36mfrom[39m [32m'../intelligence/VectorSearchEngine'[39m[33m;[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/UniversalAIBrain.integration.test.ts:6:1)

FAIL src/__tests__/integration/framework-integration.test.ts
  ● Test suite failed to run

    Cannot find module '../../brain/UniversalAIBrain' from 'src/__tests__/integration/framework-integration.test.ts'

    [0m [90m 11 |[39m [36mimport[39m { [33mMongoMemoryServer[39m } [36mfrom[39m [32m'mongodb-memory-server'[39m[33m;[39m
     [90m 12 |[39m
    [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mUniversalAIBrain[39m } [36mfrom[39m [32m'../../brain/UniversalAIBrain'[39m[33m;[39m
     [90m    |[39m [31m[1m^[22m[39m
     [90m 14 |[39m [36mimport[39m { [33mVercelAIAdapter[39m } [36mfrom[39m [32m'../../adapters/VercelAIAdapter'[39m[33m;[39m
     [90m 15 |[39m [36mimport[39m { [33mMastraAdapter[39m } [36mfrom[39m [32m'../../adapters/MastraAdapter'[39m[33m;[39m
     [90m 16 |[39m [36mimport[39m { [33mOpenAIAgentsAdapter[39m } [36mfrom[39m [32m'../../adapters/OpenAIAgentsAdapter'[39m[33m;[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/integration/framework-integration.test.ts:13:1)

FAIL src/__tests__/integration/mongodb-validation.test.ts
  ● Test suite failed to run

    Cannot find module '../../engines/VectorSearchEngine' from 'src/__tests__/integration/mongodb-validation.test.ts'

    [0m [90m 10 |[39m [36mimport[39m { [33mMongoMemoryServer[39m } [36mfrom[39m [32m'mongodb-memory-server'[39m[33m;[39m
     [90m 11 |[39m
    [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m { [33mVectorSearchEngine[39m } [36mfrom[39m [32m'../../engines/VectorSearchEngine'[39m[33m;[39m
     [90m    |[39m [31m[1m^[22m[39m
     [90m 13 |[39m [36mimport[39m { [33mTracingCollection[39m } [36mfrom[39m [32m'../../collections/TracingCollection'[39m[33m;[39m
     [90m 14 |[39m [36mimport[39m { [33mMemoryCollection[39m } [36mfrom[39m [32m'../../collections/MemoryCollection'[39m[33m;[39m
     [90m 15 |[39m [36mimport[39m { [33mPerformanceAnalyticsEngine[39m } [36mfrom[39m [32m'../../monitoring/PerformanceAnalyticsEngine'[39m[33m;[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/integration/mongodb-validation.test.ts:12:1)

FAIL src/__tests__/ProductionReadiness.test.ts
  ● Universal AI Brain - Production Readiness › Critical Component Availability › should have all framework adapters available

    Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'

    Require stack:
      src/index.ts
      src/__tests__/ProductionReadiness.test.ts

    [0m [90m 4 |[39m
     [90m 5 |[39m [90m// Core Universal AI Brain - The heart of your vision! 🧠⚡[39m
    [31m[1m>[22m[39m[90m 6 |[39m [36mexport[39m { [33mUniversalAIBrain[39m } [36mfrom[39m [32m'./brain/UniversalAIBrain'[39m[33m;[39m
     [90m   |[39m [31m[1m^[22m[39m
     [90m 7 |[39m [36mexport[39m { [33mUniversalAIBrain[39m [36mas[39m [33mUniversalAIBrainV2[39m } [36mfrom[39m [32m'./UniversalAIBrain'[39m[33m;[39m
     [90m 8 |[39m [36mexport[39m type { [33mUniversalAIBrainConfig[39m[33m,[39m [33mAIBrainResponse[39m } [36mfrom[39m [32m'./UniversalAIBrain'[39m[33m;[39m
     [90m 9 |[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/index.ts:6:1)
      at src/__tests__/ProductionReadiness.test.ts:24:91
      at Object.<anonymous> (src/__tests__/ProductionReadiness.test.ts:24:91)

  ● Universal AI Brain - Production Readiness › Critical Component Availability › should have all safety systems available

    Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'

    Require stack:
      src/index.ts
      src/__tests__/ProductionReadiness.test.ts

    [0m [90m 4 |[39m
     [90m 5 |[39m [90m// Core Universal AI Brain - The heart of your vision! 🧠⚡[39m
    [31m[1m>[22m[39m[90m 6 |[39m [36mexport[39m { [33mUniversalAIBrain[39m } [36mfrom[39m [32m'./brain/UniversalAIBrain'[39m[33m;[39m
     [90m   |[39m [31m[1m^[22m[39m
     [90m 7 |[39m [36mexport[39m { [33mUniversalAIBrain[39m [36mas[39m [33mUniversalAIBrainV2[39m } [36mfrom[39m [32m'./UniversalAIBrain'[39m[33m;[39m
     [90m 8 |[39m [36mexport[39m type { [33mUniversalAIBrainConfig[39m[33m,[39m [33mAIBrainResponse[39m } [36mfrom[39m [32m'./UniversalAIBrain'[39m[33m;[39m
     [90m 9 |[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/index.ts:6:1)
      at src/__tests__/ProductionReadiness.test.ts:33:92
      at Object.<anonymous> (src/__tests__/ProductionReadiness.test.ts:33:92)

  ● Universal AI Brain - Production Readiness › Critical Component Availability › should have all collections available

    Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'

    Require stack:
      src/index.ts
      src/__tests__/ProductionReadiness.test.ts

    [0m [90m 4 |[39m
     [90m 5 |[39m [90m// Core Universal AI Brain - The heart of your vision! 🧠⚡[39m
    [31m[1m>[22m[39m[90m 6 |[39m [36mexport[39m { [33mUniversalAIBrain[39m } [36mfrom[39m [32m'./brain/UniversalAIBrain'[39m[33m;[39m
     [90m   |[39m [31m[1m^[22m[39m
     [90m 7 |[39m [36mexport[39m { [33mUniversalAIBrain[39m [36mas[39m [33mUniversalAIBrainV2[39m } [36mfrom[39m [32m'./UniversalAIBrain'[39m[33m;[39m
     [90m 8 |[39m [36mexport[39m type { [33mUniversalAIBrainConfig[39m[33m,[39m [33mAIBrainResponse[39m } [36mfrom[39m [32m'./UniversalAIBrain'[39m[33m;[39m
     [90m 9 |[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/index.ts:6:1)
      at src/__tests__/ProductionReadiness.test.ts:45:93
      at Object.<anonymous> (src/__tests__/ProductionReadiness.test.ts:45:93)

FAIL src/__tests__/intelligence/VectorSearchEngine.test.ts
  ● Console

    console.log
      Search Analytics: semantic search for "Find relevant information" returned 1 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.error
      Failed to create embedding: Error: API Error
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:71:65)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 245 |[39m       [36mreturn[39m [36mawait[39m [36mthis[39m[33m.[39membeddingProvider[33m.[39mgenerateEmbedding(text)[33m;[39m
     [90m 246 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 247 |[39m       console[33m.[39merror([32m'Failed to create embedding:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 248 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Embedding generation failed: ${error.message}`[39m)[33m;[39m
     [90m 249 |[39m     }
     [90m 250 |[39m   }[0m

      at VectorSearchEngine.createEmbedding (src/intelligence/VectorSearchEngine.ts:247:15)
      at VectorSearchEngine.semanticSearch (src/intelligence/VectorSearchEngine.ts:136:30)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:73:23)

    console.error
      Semantic search failed: Error: Embedding generation failed: API Error
          at VectorSearchEngine.createEmbedding (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:248:13)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at VectorSearchEngine.semanticSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:136:30)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:73:23)

    [0m [90m 165 |[39m       [36mreturn[39m searchResults[33m;[39m
     [90m 166 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 167 |[39m       console[33m.[39merror([32m'Semantic search failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 168 |[39m       [36mreturn[39m [][33m;[39m
     [90m 169 |[39m     }
     [90m 170 |[39m   }[0m

      at VectorSearchEngine.semanticSearch (src/intelligence/VectorSearchEngine.ts:167:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:73:23)

    console.log
      Search Analytics: semantic search for "test query" returned 0 results in 0ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: semantic search for "test query" returned 0 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: semantic search for "test query" returned 0 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: semantic search for "test query" returned 0 results in 0ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: hybrid search for "hybrid search query" returned 1 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: hybrid search for "test query" returned 0 results in 7ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.error
      Hybrid search failed: TypeError: collection.aggregate(...).toArray is not a function
          at VectorSearchEngine.hybridSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:221:60)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:243:23)

    [0m [90m 232 |[39m       [36mreturn[39m searchResults[33m;[39m
     [90m 233 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 234 |[39m       console[33m.[39merror([32m'Hybrid search failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 235 |[39m       [90m// Fallback to semantic search[39m
     [90m 236 |[39m       [36mreturn[39m [36mawait[39m [36mthis[39m[33m.[39msemanticSearch(query[33m,[39m options)[33m;[39m
     [90m 237 |[39m     }[0m

      at VectorSearchEngine.hybridSearch (src/intelligence/VectorSearchEngine.ts:234:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:243:23)

    console.log
      Search Analytics: semantic search for "test query" returned 0 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: text search for "text search query" returned 1 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: text search for "test query" returned 0 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.error
      Text search failed: TypeError: collection.aggregate(...).toArray is not a function
          at VectorSearchEngine.textSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:317:60)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:302:48)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 334 |[39m       [36mreturn[39m searchResults[33m;[39m
     [90m 335 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 336 |[39m       console[33m.[39merror([32m'Text search failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 337 |[39m       [36mreturn[39m [][33m;[39m
     [90m 338 |[39m     }
     [90m 339 |[39m   }[0m

      at VectorSearchEngine.textSearch (src/intelligence/VectorSearchEngine.ts:336:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:302:48)

    console.error
      Failed to get search suggestions: TypeError: collection.aggregate(...).toArray is not a function
          at VectorSearchEngine.getSearchSuggestions (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:368:60)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:332:52)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 369 |[39m       [36mreturn[39m results[33m.[39mmap(doc [33m=>[39m doc[33m.[39msuggestion)[33m;[39m
     [90m 370 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 371 |[39m       console[33m.[39merror([32m'Failed to get search suggestions:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 372 |[39m       [36mreturn[39m [][33m;[39m
     [90m 373 |[39m     }
     [90m 374 |[39m   }[0m

      at VectorSearchEngine.getSearchSuggestions (src/intelligence/VectorSearchEngine.ts:371:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:332:52)

    console.error
      Failed to create embedding: Error: Embedding failed
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:348:65)
          at Promise.then.completed (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:298:28)
          at new Promise (<anonymous>)
          at callAsyncCircusFn (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\utils.js:231:10)
          at _callCircusTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:316:40)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)
          at _runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:252:3)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:126:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:121:9)
          at run (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\run.js:71:3)
          at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
          at jestAdapter (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
          at runTestInternal (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:367:16)
          at runTest (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\runTest.js:444:34)
          at Object.worker (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runner\build\testWorker.js:106:12)

    [0m [90m 245 |[39m       [36mreturn[39m [36mawait[39m [36mthis[39m[33m.[39membeddingProvider[33m.[39mgenerateEmbedding(text)[33m;[39m
     [90m 246 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 247 |[39m       console[33m.[39merror([32m'Failed to create embedding:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 248 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Embedding generation failed: ${error.message}`[39m)[33m;[39m
     [90m 249 |[39m     }
     [90m 250 |[39m   }[0m

      at VectorSearchEngine.createEmbedding (src/intelligence/VectorSearchEngine.ts:247:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:350:7)

    console.log
      Search Analytics: semantic search for "cached query" returned 1 results in 0ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: semantic search for "ttl test query" returned 1 results in 0ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.log
      Search Analytics: semantic search for "ttl test query" returned 1 results in 1ms

      at VectorSearchEngine.logSearchAnalytics (src/intelligence/VectorSearchEngine.ts:620:13)

    console.error
      Semantic search failed: Error: Database connection failed
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:439:15)
          at C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-mock\build\index.js:397:39
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-mock\build\index.js:404:13)
          at Object.mockConstructor [as collection] (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-mock\build\index.js:148:19)
          at VectorSearchEngine.semanticSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:153:34)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:442:23)

    [0m [90m 165 |[39m       [36mreturn[39m searchResults[33m;[39m
     [90m 166 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 167 |[39m       console[33m.[39merror([32m'Semantic search failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 168 |[39m       [36mreturn[39m [][33m;[39m
     [90m 169 |[39m     }
     [90m 170 |[39m   }[0m

      at VectorSearchEngine.semanticSearch (src/intelligence/VectorSearchEngine.ts:167:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:442:23)

    console.error
      Semantic search failed: TypeError: Cannot read properties of null (reading '_id')
          at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:572:15
          at Array.map (<anonymous>)
          at VectorSearchEngine.processSearchResults (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:571:20)
          at VectorSearchEngine.semanticSearch (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\intelligence\VectorSearchEngine.ts:157:34)
          at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\intelligence\VectorSearchEngine.test.ts:458:23)

    [0m [90m 165 |[39m       [36mreturn[39m searchResults[33m;[39m
     [90m 166 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 167 |[39m       console[33m.[39merror([32m'Semantic search failed:'[39m[33m,[39m error)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 168 |[39m       [36mreturn[39m [][33m;[39m
     [90m 169 |[39m     }
     [90m 170 |[39m   }[0m

      at VectorSearchEngine.semanticSearch (src/intelligence/VectorSearchEngine.ts:167:15)
      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:458:23)

  ● VectorSearchEngine › Semantic Search › should include embeddings when requested

    expect(received).toHaveProperty(path)

    Expected path: "embedding.values"
    Received path: []

    Received value: {"_id": 1, "content": 1, "embedding.values": 1, "metadata": 1, "vectorScore": 1}

    [0m [90m 148 |[39m       [36mconst[39m projectStage [33m=[39m aggregateCall[aggregateCall[33m.[39mlength [33m-[39m [35m1[39m][33m;[39m
     [90m 149 |[39m
    [31m[1m>[22m[39m[90m 150 |[39m       expect(projectStage[33m.[39m$project)[33m.[39mtoHaveProperty([32m'embedding.values'[39m)[33m;[39m
     [90m     |[39m                                     [31m[1m^[22m[39m
     [90m 151 |[39m     })[33m;[39m
     [90m 152 |[39m
     [90m 153 |[39m     it([32m'should include explanations when requested'[39m[33m,[39m [36masync[39m () [33m=>[39m {[0m

      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:150:37)

  ● VectorSearchEngine › Search Caching › should respect cache TTL

    Hybrid search failed

    [0m [90m 234 |[39m
     [90m 235 |[39m     it([32m'should fallback to semantic search on hybrid failure'[39m[33m,[39m [36masync[39m () [33m=>[39m {
    [31m[1m>[22m[39m[90m 236 |[39m       mockCollection[33m.[39maggregate[33m.[39mmockRejectedValueOnce([36mnew[39m [33mError[39m([32m'Hybrid search failed'[39m))[33m;[39m
     [90m     |[39m                                                      [31m[1m^[22m[39m
     [90m 237 |[39m       
     [90m 238 |[39m       [90m// Mock successful semantic search[39m
     [90m 239 |[39m       mockCollection[33m.[39maggregate[33m.[39mmockReturnValueOnce({[0m

      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:236:54)

  ● VectorSearchEngine › Search Caching › should respect cache TTL

    Text search failed

    [0m [90m 298 |[39m
     [90m 299 |[39m     it([32m'should handle text search failure'[39m[33m,[39m [36masync[39m () [33m=>[39m {
    [31m[1m>[22m[39m[90m 300 |[39m       mockCollection[33m.[39maggregate[33m.[39mmockRejectedValue([36mnew[39m [33mError[39m([32m'Text search failed'[39m))[33m;[39m
     [90m     |[39m                                                  [31m[1m^[22m[39m
     [90m 301 |[39m
     [90m 302 |[39m       [36mconst[39m results [33m=[39m [36mawait[39m vectorSearchEngine[33m.[39mtextSearch([32m'test query'[39m)[33m;[39m
     [90m 303 |[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:300:50)

  ● VectorSearchEngine › Search Caching › should respect cache TTL

    Suggestion failed

    [0m [90m 328 |[39m
     [90m 329 |[39m     it([32m'should handle suggestion failure gracefully'[39m[33m,[39m [36masync[39m () [33m=>[39m {
    [31m[1m>[22m[39m[90m 330 |[39m       mockCollection[33m.[39maggregate[33m.[39mmockRejectedValue([36mnew[39m [33mError[39m([32m'Suggestion failed'[39m))[33m;[39m
     [90m     |[39m                                                  [31m[1m^[22m[39m
     [90m 331 |[39m
     [90m 332 |[39m       [36mconst[39m suggestions [33m=[39m [36mawait[39m vectorSearchEngine[33m.[39mgetSearchSuggestions([32m'test'[39m)[33m;[39m
     [90m 333 |[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:330:50)

  ● VectorSearchEngine › Error Handling › should handle malformed search results

    expect(received).toHaveLength(expected)

    Expected length: 1
    Received length: 0
    Received array:  []

    [0m [90m 459 |[39m
     [90m 460 |[39m       [90m// Should handle malformed results gracefully[39m
    [31m[1m>[22m[39m[90m 461 |[39m       expect(results)[33m.[39mtoHaveLength([35m1[39m)[33m;[39m
     [90m     |[39m                       [31m[1m^[22m[39m
     [90m 462 |[39m       expect(results[[35m0[39m][33m.[39mcontent)[33m.[39mtoBe([32m''[39m)[33m;[39m
     [90m 463 |[39m       expect(results[[35m0[39m][33m.[39mscore)[33m.[39mtoBe([35m0[39m)[33m;[39m
     [90m 464 |[39m     })[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/VectorSearchEngine.test.ts:461:23)

FAIL src/__tests__/FrameworkAdapters.test.ts
  ● Test suite failed to run

    Cannot find module '../brain/UniversalAIBrain' from 'src/__tests__/FrameworkAdapters.test.ts'

    [0m [90m 10 |[39m
     [90m 11 |[39m [36mimport[39m { setupTestDb[33m,[39m teardownTestDb } [36mfrom[39m [32m'./setup'[39m[33m;[39m
    [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m { [33mUniversalAIBrain[39m[33m,[39m [33mBrainConfig[39m } [36mfrom[39m [32m'../brain/UniversalAIBrain'[39m[33m;[39m
     [90m    |[39m [31m[1m^[22m[39m
     [90m 13 |[39m [36mimport[39m { [33mVercelAIAdapter[39m } [36mfrom[39m [32m'../adapters/VercelAIAdapter'[39m[33m;[39m
     [90m 14 |[39m [36mimport[39m { [33mMastraAdapter[39m } [36mfrom[39m [32m'../adapters/MastraAdapter'[39m[33m;[39m
     [90m 15 |[39m [36mimport[39m { [33mOpenAIAgentsAdapter[39m } [36mfrom[39m [32m'../adapters/OpenAIAgentsAdapter'[39m[33m;[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/FrameworkAdapters.test.ts:12:1)

C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:427
    throw new _ModuleNotFoundError.default(
          ^

ModuleNotFoundError: Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'

Require stack:
  src/index.ts
  src/__tests__/ExportValidation.test.ts

    at Resolver._throwModNotFoundError (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:427:11)
    at Resolver.resolveModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:358:10)
    at Resolver._getVirtualMockPath (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:619:14)
    at Resolver._getAbsolutePath (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:587:14)
    at Resolver.getModuleID (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:530:31)
    at Runtime._shouldMockCjs (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1713:37)
    at Runtime.requireModuleOrMock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1045:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\index.ts:6:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:882:12)
    at Runtime.requireModuleOrMock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1048:21)
    at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\ExportValidation.test.ts:338:9
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\ExportValidation.test.ts:338:9 {
  code: 'MODULE_NOT_FOUND',
  hint: '',
  requireStack: [
    'C:\\Users\\<USER>\\Desktop\\boiler\\boiler_plate\\packages\\core\\src\\index.ts',
    'C:\\Users\\<USER>\\Desktop\\boiler\\boiler_plate\\packages\\core\\src\\__tests__\\ExportValidation.test.ts'
  ],
  siblingWithSimilarExtensionFound: false,
  moduleName: './brain/UniversalAIBrain',
  _originalMessage: "Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'"
}

Node.js v22.11.0
FAIL src/__tests__/integration/complete-system-validation.test.ts
  ● Test suite failed to run

    Cannot find module '../../brain/UniversalAIBrain' from 'src/__tests__/integration/complete-system-validation.test.ts'

    [0m [90m 10 |[39m [36mimport[39m { [33mMongoMemoryServer[39m } [36mfrom[39m [32m'mongodb-memory-server'[39m[33m;[39m
     [90m 11 |[39m
    [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m { [33mUniversalAIBrain[39m } [36mfrom[39m [32m'../../brain/UniversalAIBrain'[39m[33m;[39m
     [90m    |[39m [31m[1m^[22m[39m
     [90m 13 |[39m [36mimport[39m { [33mVercelAIAdapter[39m } [36mfrom[39m [32m'../../adapters/VercelAIAdapter'[39m[33m;[39m
     [90m 14 |[39m [36mimport[39m { [33mMastraAdapter[39m } [36mfrom[39m [32m'../../adapters/MastraAdapter'[39m[33m;[39m
     [90m 15 |[39m[0m

      at Resolver._throwModNotFoundError (../../node_modules/jest-resolve/build/resolver.js:427:11)
      at Object.<anonymous> (src/__tests__/integration/complete-system-validation.test.ts:12:1)

FAIL src/__tests__/intelligence/SemanticMemoryEngine.test.ts (5.109 s)
  ● Console

    console.log
      ✅ Connected to MongoDB Atlas test database: universal_ai_brain_test

      at setupTestDatabase (src/__tests__/testConfig.ts:47:13)

    console.warn
      ⚠️ No schema found for collection agent_memory

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at MemoryCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at MemoryCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new MemoryCollection (src/collections/MemoryCollection.ts:56:10)
      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:28:26)

    console.log
      🧹 Cleaned up test collections

      at cleanupTestDatabase (src/__tests__/testConfig.ts:65:15)

    console.log
      ✅ Disconnected from MongoDB Atlas

      at cleanupTestDatabase (src/__tests__/testConfig.ts:76:15)

  ● SemanticMemoryEngine › Memory Storage › should store memory with embedding generation

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Storage › should handle embedding generation failure gracefully

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Storage › should store memory without embedding when disabled

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Retrieval › should retrieve relevant memories using vector search

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Retrieval › should handle vector search failure with fallback

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Retrieval › should filter memories by type and framework

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Importance Management › should update memory importance with decay factor

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Importance Management › should throw error for non-existent memory

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Relationships › should create bidirectional relationships between memories

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Relationships › should handle missing memories in relationship creation

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Analytics › should generate comprehensive memory analytics

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › Memory Analytics › should handle empty analytics gracefully

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › MongoDB Vector Search Integration › should construct proper $vectorSearch aggregation pipeline

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

  ● SemanticMemoryEngine › MongoDB Vector Search Integration › should include proper vector score calculation

    TypeError: memoryCollection.initialize is not a function

    [0m [90m 30 |[39m
     [90m 31 |[39m       [90m// Initialize collection with indexes[39m
    [31m[1m>[22m[39m[90m 32 |[39m       [36mawait[39m memoryCollection[33m.[39minitialize()[33m;[39m
     [90m    |[39m                              [31m[1m^[22m[39m
     [90m 33 |[39m
     [90m 34 |[39m       semanticMemoryEngine [33m=[39m [36mnew[39m [33mSemanticMemoryEngine[39m(memoryCollection[33m,[39m embeddingProvider)[33m;[39m
     [90m 35 |[39m       console[33m.[39mlog([32m'✅ SemanticMemoryEngine test setup complete'[39m)[33m;[39m[0m

      at Object.<anonymous> (src/__tests__/intelligence/SemanticMemoryEngine.test.ts:32:30)

(node:512) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:427
    throw new _ModuleNotFoundError.default(
          ^

ModuleNotFoundError: Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'

Require stack:
  src/index.ts
  src/__tests__/ExportValidation.test.ts

    at Resolver._throwModNotFoundError (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:427:11)
    at Resolver.resolveModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:358:10)
    at Resolver._getVirtualMockPath (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:619:14)
    at Resolver._getAbsolutePath (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:587:14)
    at Resolver.getModuleID (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:530:31)
    at Runtime._shouldMockCjs (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1713:37)
    at Runtime.requireModuleOrMock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1045:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\index.ts:6:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:882:12)
    at Runtime.requireModuleOrMock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1048:21)
    at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\ExportValidation.test.ts:338:9
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\ExportValidation.test.ts:338:9 {
  code: 'MODULE_NOT_FOUND',
  hint: '',
  requireStack: [
    'C:\\Users\\<USER>\\Desktop\\boiler\\boiler_plate\\packages\\core\\src\\index.ts',
    'C:\\Users\\<USER>\\Desktop\\boiler\\boiler_plate\\packages\\core\\src\\__tests__\\ExportValidation.test.ts'
  ],
  siblingWithSimilarExtensionFound: false,
  moduleName: './brain/UniversalAIBrain',
  _originalMessage: "Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'"
}

Node.js v22.11.0
PASS src/__tests__/CoreCollections.test.ts (9.273 s)
  ● Console

    console.warn
      ⚠️ No schema found for collection agents

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at AgentCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at AgentCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new AgentCollection (src/collections/AgentCollection.ts:48:10)
      at Object.<anonymous> (src/__tests__/CoreCollections.test.ts:28:23)

    console.warn
      ⚠️ No schema found for collection agent_memory

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at MemoryCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at MemoryCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new MemoryCollection (src/collections/MemoryCollection.ts:56:10)
      at Object.<anonymous> (src/__tests__/CoreCollections.test.ts:29:24)

    console.warn
      ⚠️ No schema found for collection agent_workflows

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at WorkflowCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at WorkflowCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new WorkflowCollection (src/collections/WorkflowCollection.ts:60:10)
      at Object.<anonymous> (src/__tests__/CoreCollections.test.ts:30:26)

    console.warn
      ⚠️ No schema found for collection agent_tools

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at ToolCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at ToolCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new ToolCollection (src/collections/ToolCollection.ts:66:10)
      at Object.<anonymous> (src/__tests__/CoreCollections.test.ts:31:22)

    console.warn
      ⚠️ No schema found for collection agent_performance_metrics

    [0m [90m 74 |[39m       [36mthis[39m[33m.[39mschema [33m=[39m require(schemaPath)[33m;[39m
     [90m 75 |[39m     } [36mcatch[39m (error) {
    [31m[1m>[22m[39m[90m 76 |[39m       console[33m.[39mwarn([32m`⚠️ No schema found for collection ${this.collectionName}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 77 |[39m     }
     [90m 78 |[39m   }
     [90m 79 |[39m[0m

      at MetricsCollection.loadSchema (src/collections/BaseCollection.ts:76:15)
      at MetricsCollection.initializeCollection (src/collections/BaseCollection.ts:63:12)
      at new MetricsCollection (src/collections/MetricsCollection.ts:47:10)
      at Object.<anonymous> (src/__tests__/CoreCollections.test.ts:32:25)

(node:14252) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:427
    throw new _ModuleNotFoundError.default(
          ^

ModuleNotFoundError: Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'

Require stack:
  src/index.ts
  src/__tests__/ExportValidation.test.ts

    at Resolver._throwModNotFoundError (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:427:11)
    at Resolver.resolveModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:358:10)
    at Resolver._getVirtualMockPath (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:619:14)
    at Resolver._getAbsolutePath (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:587:14)
    at Resolver.getModuleID (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-resolve\build\resolver.js:530:31)
    at Runtime._shouldMockCjs (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1713:37)
    at Runtime.requireModuleOrMock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1045:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\index.ts:6:1)
    at Runtime._execModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1439:24)
    at Runtime._loadModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1022:12)
    at Runtime.requireModule (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:882:12)
    at Runtime.requireModuleOrMock (C:\Users\<USER>\Desktop\boiler\boiler_plate\node_modules\jest-runtime\build\index.js:1048:21)
    at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\ExportValidation.test.ts:338:9
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C:\Users\<USER>\Desktop\boiler\boiler_plate\packages\core\src\__tests__\ExportValidation.test.ts:338:9 {
  code: 'MODULE_NOT_FOUND',
  hint: '',
  requireStack: [
    'C:\\Users\\<USER>\\Desktop\\boiler\\boiler_plate\\packages\\core\\src\\index.ts',
    'C:\\Users\\<USER>\\Desktop\\boiler\\boiler_plate\\packages\\core\\src\\__tests__\\ExportValidation.test.ts'
  ],
  siblingWithSimilarExtensionFound: false,
  moduleName: './brain/UniversalAIBrain',
  _originalMessage: "Cannot find module './brain/UniversalAIBrain' from 'src/index.ts'"
}

Node.js v22.11.0
^C
