/**
 * @file VectorSearchEngine Tests
 * Comprehensive tests for the VectorSearchEngine component
 */

import { VectorSearchEngine, SearchResult, SearchOptions, HybridSearchOptions } from '../../intelligence/VectorSearchEngine';
import { Db, Collection } from 'mongodb';
import { OpenAIEmbeddingProvider } from '../../embeddings/OpenAIEmbeddingProvider';

// Mock dependencies
jest.mock('mongodb');
jest.mock('../../embeddings/OpenAIEmbeddingProvider');

describe('VectorSearchEngine', () => {
  let vectorSearchEngine: VectorSearchEngine;
  let mockDb: jest.Mocked<Db>;
  let mockCollection: jest.Mocked<Collection>;
  let mockEmbeddingProvider: jest.Mocked<OpenAIEmbeddingProvider>;

  beforeEach(() => {
    mockCollection = {
      aggregate: jest.fn(),
      find: jest.fn(),
      insertOne: jest.fn(),
      updateOne: jest.fn(),
      deleteMany: jest.fn()
    } as any;

    mockDb = {
      collection: jest.fn().mockReturnValue(mockCollection)
    } as any;

    mockEmbeddingProvider = new OpenAIEmbeddingProvider({ apiKey: 'test' }) as jest.Mocked<OpenAIEmbeddingProvider>;
    mockEmbeddingProvider.generateEmbedding = jest.fn().mockResolvedValue([0.1, 0.2, 0.3]);

    vectorSearchEngine = new VectorSearchEngine(mockDb, mockEmbeddingProvider);
  });

  describe('Semantic Search', () => {
    it('should perform semantic search with vector similarity', async () => {
      const mockResults = [
        {
          _id: 'doc1',
          content: { text: 'Relevant document' },
          metadata: { source: 'knowledge_base' },
          vectorScore: 0.85
        }
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockResults)
      } as any);

      const query = 'Find relevant information';
      const options: SearchOptions = {
        limit: 10,
        minScore: 0.7,
        includeEmbeddings: false
      };

      const results = await vectorSearchEngine.semanticSearch(query, options);

      expect(mockEmbeddingProvider.generateEmbedding).toHaveBeenCalledWith(query);
      expect(mockCollection.aggregate).toHaveBeenCalled();
      expect(results).toHaveLength(1);
      expect(results[0].content).toBe('Relevant document');
      expect(results[0].score).toBe(0.85);
    });

    it('should handle embedding generation failure', async () => {
      mockEmbeddingProvider.generateEmbedding.mockRejectedValue(new Error('API Error'));

      const results = await vectorSearchEngine.semanticSearch('test query');

      expect(results).toEqual([]);
    });

    it('should construct proper $vectorSearch pipeline', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      const query = 'test query';
      const options: SearchOptions = {
        limit: 5,
        minScore: 0.8,
        maxCandidates: 100,
        filters: { framework: 'vercel-ai' },
        boost: [{ field: 'importance', factor: 1.5 }]
      };

      await vectorSearchEngine.semanticSearch(query, options);

      const aggregateCall = mockCollection.aggregate.mock.calls[0][0];
      const vectorSearchStage = aggregateCall[0];

      expect(vectorSearchStage).toHaveProperty('$vectorSearch');
      expect(vectorSearchStage.$vectorSearch).toEqual(
        expect.objectContaining({
          index: 'vector_search_index',
          path: 'embedding.values',
          queryVector: [0.1, 0.2, 0.3],
          numCandidates: 100,
          limit: 10, // limit * 2
          filter: { framework: 'vercel-ai' }
        })
      );
    });

    it('should apply boost factors correctly', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      const options: SearchOptions = {
        boost: [
          { field: 'importance', factor: 1.5 },
          { field: 'recency', factor: 0.8 }
        ]
      };

      await vectorSearchEngine.semanticSearch('test query', options);

      const aggregateCall = mockCollection.aggregate.mock.calls[0][0];
      const boostStage = aggregateCall.find((stage: any) => stage.$addFields?.boostedScore);

      expect(boostStage).toBeDefined();
      expect(boostStage.$addFields.boostedScore).toEqual(
        expect.objectContaining({
          $add: expect.arrayContaining([
            '$vectorScore',
            expect.any(Object)
          ])
        })
      );
    });

    it('should include embeddings when requested', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      await vectorSearchEngine.semanticSearch('test query', {
        includeEmbeddings: true
      });

      const aggregateCall = mockCollection.aggregate.mock.calls[0][0];
      const projectStage = aggregateCall[aggregateCall.length - 1];

      expect(projectStage.$project).toHaveProperty('embedding.values');
    });

    it('should include explanations when requested', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      await vectorSearchEngine.semanticSearch('test query', {
        includeExplanation: true
      });

      const aggregateCall = mockCollection.aggregate.mock.calls[0][0];
      const projectStage = aggregateCall[aggregateCall.length - 1];

      expect(projectStage.$project).toHaveProperty('explanation');
    });
  });

  describe('Hybrid Search', () => {
    it('should perform hybrid vector + text search', async () => {
      const mockResults = [
        {
          _id: 'doc1',
          content: { text: 'Hybrid search result' },
          metadata: { source: 'knowledge_base' },
          vectorScore: 0.8,
          textScore: 0.7,
          combinedScore: 0.75
        }
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockResults)
      } as any);

      const query = 'hybrid search query';
      const options: HybridSearchOptions = {
        limit: 10,
        vectorWeight: 0.7,
        textWeight: 0.3,
        textQuery: 'custom text query'
      };

      const results = await vectorSearchEngine.hybridSearch(query, options);

      expect(mockEmbeddingProvider.generateEmbedding).toHaveBeenCalledWith(query);
      expect(mockCollection.aggregate).toHaveBeenCalled();
      expect(results).toHaveLength(1);
      expect(results[0].score).toBe(0.75);
    });

    it('should construct proper hybrid search pipeline', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      const options: HybridSearchOptions = {
        vectorWeight: 0.6,
        textWeight: 0.4,
        textQuery: 'text search',
        filters: { category: 'documents' }
      };

      await vectorSearchEngine.hybridSearch('test query', options);

      const aggregateCall = mockCollection.aggregate.mock.calls[0][0];
      
      // Should have vector search stage
      const vectorSearchStage = aggregateCall.find((stage: any) => stage.$vectorSearch);
      expect(vectorSearchStage).toBeDefined();

      // Should have text search stage
      const textSearchStage = aggregateCall.find((stage: any) => stage.$search);
      expect(textSearchStage).toBeDefined();

      // Should have combined score calculation
      const combinedScoreStage = aggregateCall.find((stage: any) => stage.$addFields?.combinedScore);
      expect(combinedScoreStage).toBeDefined();
      expect(combinedScoreStage.$addFields.combinedScore.$add).toEqual([
        { $multiply: ['$vectorScore', 0.6] },
        { $multiply: ['$textScore', 0.4] }
      ]);
    });

    it('should fallback to semantic search on hybrid failure', async () => {
      mockCollection.aggregate.mockRejectedValueOnce(new Error('Hybrid search failed'));
      
      // Mock successful semantic search
      mockCollection.aggregate.mockReturnValueOnce({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      const results = await vectorSearchEngine.hybridSearch('test query');

      expect(results).toEqual([]);
      expect(mockCollection.aggregate).toHaveBeenCalledTimes(2); // Failed hybrid + successful semantic
    });
  });

  describe('Text Search', () => {
    it('should perform text-only search', async () => {
      const mockResults = [
        {
          _id: 'doc1',
          content: { text: 'Text search result' },
          metadata: { source: 'documents' },
          textScore: 0.9
        }
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockResults)
      } as any);

      const query = 'text search query';
      const options: SearchOptions = {
        limit: 5,
        filters: { category: 'documents' }
      };

      const results = await vectorSearchEngine.textSearch(query, options);

      expect(mockCollection.aggregate).toHaveBeenCalled();
      expect(results).toHaveLength(1);
      expect(results[0].content).toBe('Text search result');
      expect(results[0].score).toBe(0.9);
    });

    it('should construct proper text search pipeline', async () => {
      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      const options: SearchOptions = {
        filters: { category: 'documents', status: 'active' }
      };

      await vectorSearchEngine.textSearch('test query', options);

      const aggregateCall = mockCollection.aggregate.mock.calls[0][0];
      const searchStage = aggregateCall[0];

      expect(searchStage).toHaveProperty('$search');
      expect(searchStage.$search.index).toBe('text_search_index');
      expect(searchStage.$search.compound.must[0].text.query).toBe('test query');
      expect(searchStage.$search.compound.filter).toEqual([options.filters]);
    });

    it('should handle text search failure', async () => {
      mockCollection.aggregate.mockRejectedValue(new Error('Text search failed'));

      const results = await vectorSearchEngine.textSearch('test query');

      expect(results).toEqual([]);
    });
  });

  describe('Search Suggestions', () => {
    it('should provide search suggestions', async () => {
      const mockSuggestions = [
        { suggestion: 'machine learning algorithms' },
        { suggestion: 'machine learning models' },
        { suggestion: 'machine learning applications' }
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockSuggestions)
      } as any);

      const suggestions = await vectorSearchEngine.getSearchSuggestions('machine learn', 3);

      expect(suggestions).toEqual([
        'machine learning algorithms',
        'machine learning models',
        'machine learning applications'
      ]);
    });

    it('should handle suggestion failure gracefully', async () => {
      mockCollection.aggregate.mockRejectedValue(new Error('Suggestion failed'));

      const suggestions = await vectorSearchEngine.getSearchSuggestions('test');

      expect(suggestions).toEqual([]);
    });
  });

  describe('Embedding Creation', () => {
    it('should create embeddings successfully', async () => {
      const text = 'Test text for embedding';
      const embedding = await vectorSearchEngine.createEmbedding(text);

      expect(mockEmbeddingProvider.generateEmbedding).toHaveBeenCalledWith(text);
      expect(embedding).toEqual([0.1, 0.2, 0.3]);
    });

    it('should handle embedding creation failure', async () => {
      mockEmbeddingProvider.generateEmbedding.mockRejectedValue(new Error('Embedding failed'));

      await expect(vectorSearchEngine.createEmbedding('test text')).rejects.toThrow('Embedding generation failed');
    });
  });

  describe('Search Caching', () => {
    it('should cache search results', async () => {
      const mockResults = [
        {
          _id: 'doc1',
          content: { text: 'Cached result' },
          metadata: {},
          vectorScore: 0.8
        }
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockResults)
      } as any);

      const query = 'cached query';
      const options: SearchOptions = { limit: 5 };

      // First call
      const results1 = await vectorSearchEngine.semanticSearch(query, options);
      
      // Second call with same parameters
      const results2 = await vectorSearchEngine.semanticSearch(query, options);

      expect(results1).toEqual(results2);
      expect(mockCollection.aggregate).toHaveBeenCalledTimes(1); // Should be cached
    });

    it('should respect cache TTL', async () => {
      const mockResults = [
        {
          _id: 'doc1',
          content: { text: 'TTL test result' },
          metadata: {},
          vectorScore: 0.8
        }
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(mockResults)
      } as any);

      // Mock cache TTL to be very short for testing
      const originalCacheTTL = (vectorSearchEngine as any).cacheTTL;
      (vectorSearchEngine as any).cacheTTL = 1; // 1ms

      const query = 'ttl test query';
      
      // First call
      await vectorSearchEngine.semanticSearch(query);
      
      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 2));
      
      // Second call after cache expiry
      await vectorSearchEngine.semanticSearch(query);

      expect(mockCollection.aggregate).toHaveBeenCalledTimes(2); // Should not be cached

      // Restore original TTL
      (vectorSearchEngine as any).cacheTTL = originalCacheTTL;
    });
  });

  describe('Search Analytics', () => {
    it('should log search analytics', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue([])
      } as any);

      await vectorSearchEngine.semanticSearch('analytics test query');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Search Analytics: semantic search')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      mockDb.collection.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const results = await vectorSearchEngine.semanticSearch('test query');

      expect(results).toEqual([]);
    });

    it('should handle malformed search results', async () => {
      const malformedResults = [
        { _id: 'doc1' }, // Missing required fields
        null,
        undefined
      ];

      mockCollection.aggregate.mockReturnValue({
        toArray: jest.fn().mockResolvedValue(malformedResults)
      } as any);

      const results = await vectorSearchEngine.semanticSearch('test query');

      // Should handle malformed results gracefully
      expect(results).toHaveLength(1);
      expect(results[0].content).toBe('');
      expect(results[0].score).toBe(0);
    });
  });
});
