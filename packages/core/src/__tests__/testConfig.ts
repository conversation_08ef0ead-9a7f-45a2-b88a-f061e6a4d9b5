/**
 * @file Test Configuration for Universal AI Brain
 * Real MongoDB Atlas and OpenAI configuration for production testing
 */

import { MongoClient, Db } from 'mongodb';
import { OpenAIEmbeddingProvider } from '../embeddings/OpenAIEmbeddingProvider';

export interface TestConfig {
  mongoUri: string;
  databaseName: string;
  openaiApiKey: string;
  testTimeout: number;
}

export const testConfig: TestConfig = {
  mongoUri: process.env.MONGODB_URI || 'mongodb+srv://romiluz:<EMAIL>/?retryWrites=true&w=majority&appName=agents',
  databaseName: process.env.DATABASE_NAME || 'universal_ai_brain_test',
  openaiApiKey: process.env.OPENAI_API_KEY || '',
  testTimeout: 60000
};

// Global test database connection
let testDb: Db | null = null;
let testClient: MongoClient | null = null;

export async function setupTestDatabase(): Promise<Db> {
  if (testDb) {
    return testDb;
  }

  try {
    testClient = new MongoClient(testConfig.mongoUri, {
      maxPoolSize: 5,
      minPoolSize: 1,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      retryReads: true
    });

    await testClient.connect();
    testDb = testClient.db(testConfig.databaseName);
    
    console.log(`✅ Connected to MongoDB Atlas test database: ${testConfig.databaseName}`);
    return testDb;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB Atlas:', error);
    throw error;
  }
}

export async function cleanupTestDatabase(): Promise<void> {
  if (testDb) {
    try {
      // Clean up test collections
      const collections = await testDb.listCollections().toArray();
      for (const collection of collections) {
        if (collection.name.includes('test') || collection.name.includes('temp')) {
          await testDb.collection(collection.name).deleteMany({});
        }
      }
      console.log('🧹 Cleaned up test collections');
    } catch (error) {
      console.warn('⚠️ Warning: Could not clean up test collections:', error);
    }
  }

  if (testClient) {
    try {
      await testClient.close();
      testClient = null;
      testDb = null;
      console.log('✅ Disconnected from MongoDB Atlas');
    } catch (error) {
      console.warn('⚠️ Warning: Could not close MongoDB connection:', error);
    }
  }
}

export function createTestEmbeddingProvider(): OpenAIEmbeddingProvider {
  return new OpenAIEmbeddingProvider({
    apiKey: testConfig.openaiApiKey,
    model: 'text-embedding-3-small', // Use smaller model for faster tests
    dimensions: 1536
  });
}

export function generateTestId(): string {
  return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function createTestMemory() {
  return {
    id: generateTestId(),
    content: 'Test memory content for Universal AI Brain',
    metadata: {
      type: 'test' as const,
      importance: 0.8,
      confidence: 0.9,
      source: 'test_suite',
      framework: 'universal',
      sessionId: generateTestId(),
      tags: ['test', 'memory'],
      relationships: [],
      accessCount: 0,
      lastAccessed: new Date(),
      created: new Date(),
      updated: new Date()
    }
  };
}

export function createTestContext() {
  return {
    contextId: generateTestId(),
    content: 'Test context for Universal AI Brain',
    source: 'test_suite',
    relevanceScore: 0.85,
    metadata: {
      type: 'test' as const,
      framework: 'universal',
      sessionId: generateTestId(),
      userId: 'test_user',
      tags: ['test', 'context'],
      importance: 0.8,
      confidence: 0.9,
      lastUsed: new Date(),
      usageCount: 0
    },
    embedding: {
      values: Array.from({ length: 1536 }, () => Math.random() - 0.5),
      model: 'text-embedding-3-small',
      dimensions: 1536
    }
  };
}

// Test utilities for MongoDB Atlas Vector Search
export const vectorSearchTestUtils = {
  createVectorSearchIndex: async (db: Db, collectionName: string, indexName: string) => {
    try {
      // Note: Vector search indexes must be created through Atlas UI or API
      // This is a placeholder for documentation
      console.log(`📝 Vector search index '${indexName}' should be created for collection '${collectionName}'`);
      console.log('Index definition:');
      console.log(JSON.stringify({
        mappings: {
          fields: {
            'embedding.values': {
              type: 'knnVector',
              dimensions: 1536,
              similarity: 'cosine'
            }
          }
        }
      }, null, 2));
    } catch (error) {
      console.warn(`⚠️ Could not create vector search index: ${error}`);
    }
  },

  testVectorSearchQuery: (queryVector: number[]) => [
    {
      $vectorSearch: {
        index: 'memory_vector_index',
        path: 'embedding.values',
        queryVector: queryVector,
        numCandidates: 150,
        limit: 10,
        filter: { 'metadata.type': 'test' }
      }
    },
    {
      $addFields: {
        vectorScore: { $meta: 'vectorSearchScore' }
      }
    },
    {
      $match: {
        vectorScore: { $gte: 0.7 }
      }
    }
  ]
};

// Error handling utilities
export function isMongoAtlasError(error: any): boolean {
  return error?.message?.includes('$vectorSearch') || 
         error?.message?.includes('Atlas') ||
         error?.code === 40324; // Vector search not available
}

export function isOpenAIError(error: any): boolean {
  return error?.message?.includes('OpenAI') ||
         error?.status === 401 ||
         error?.status === 429;
}

export function shouldSkipTest(error: any): boolean {
  // Skip tests that require Atlas Vector Search if not available
  if (isMongoAtlasError(error)) {
    console.log('⏭️ Skipping test: MongoDB Atlas Vector Search not available');
    return true;
  }
  
  // Skip tests that require OpenAI if API key is invalid
  if (isOpenAIError(error)) {
    console.log('⏭️ Skipping test: OpenAI API not available');
    return true;
  }
  
  return false;
}
