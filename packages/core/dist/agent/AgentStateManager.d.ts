import { ObjectId } from 'mongodb';
import { IDataStore } from '../persistance/IDataStore';
export interface AgentState {
    _id?: ObjectId;
    agent_id: string;
    name: string;
    description?: string;
    version: string;
    status: 'active' | 'inactive' | 'deprecated';
    created_at?: Date;
    updated_at?: Date;
    capabilities?: string[];
    tools?: {
        tool_id: string;
        name: string;
        config?: Record<string, any>;
        rate_limits?: {
            calls_per_minute?: number;
            calls_per_day?: number;
        };
    }[];
    model_config: {
        provider: string;
        model: string;
        temperature?: number;
        max_tokens?: number;
        system_prompt?: string;
    };
    performance_targets?: {
        max_response_time_seconds?: number;
        min_confidence_score?: number;
        max_cost_per_execution?: number;
    };
}
export interface AgentConfiguration {
    _id?: ObjectId;
    config_id: string;
    agent_id: string;
    version: string;
    is_active: boolean;
    created_at?: Date;
    prompts: {
        system_prompt: string;
        task_prompt?: string;
        analysis_prompt?: string;
        fallback_prompt?: string;
    };
    parameters?: {
        confidence_threshold?: number;
        max_iterations?: number;
        temperature?: number;
        max_tokens?: number;
    };
    quality_gates?: {
        min_confidence?: number;
        required_fields?: string[];
        validation_rules?: string[];
    };
    network_policies?: {
        allowed_hosts?: string[];
        blocked_hosts?: string[];
        require_approval?: boolean;
    };
}
export declare class AgentStateManager {
    private agentStore;
    private configStore;
    private agentState;
    private agentConfig;
    constructor(agentStore: IDataStore<AgentState>, configStore: IDataStore<AgentConfiguration>);
    /**
     * Load agent state and active configuration
     */
    loadState(agentId: string): Promise<void>;
    /**
     * Get the current agent state
     */
    getState(): AgentState;
    /**
     * Get the current agent configuration
     */
    getConfiguration(): AgentConfiguration | null;
    /**
     * Get the effective system prompt (from config if available, otherwise from agent)
     */
    getSystemPrompt(): string;
    /**
     * Get effective model configuration
     */
    getModelConfig(): AgentState['model_config'];
    /**
     * Get agent tools
     */
    getTools(): AgentState['tools'];
    /**
     * Get agent capabilities
     */
    getCapabilities(): string[];
    /**
     * Get performance targets
     */
    getPerformanceTargets(): AgentState['performance_targets'];
    /**
     * Check if agent is active
     */
    isActive(): boolean;
    /**
     * Update agent status
     */
    updateStatus(status: AgentState['status']): Promise<void>;
    /**
     * Create a new agent
     */
    createAgent(agentData: Omit<AgentState, '_id' | 'created_at' | 'updated_at'>): Promise<AgentState>;
    /**
     * Create a new agent configuration
     */
    createConfiguration(configData: Omit<AgentConfiguration, '_id' | 'created_at'>): Promise<AgentConfiguration>;
    /**
     * Get quality gates for validation
     */
    getQualityGates(): AgentConfiguration['quality_gates'];
    /**
     * Get network policies for security
     */
    getNetworkPolicies(): AgentConfiguration['network_policies'];
    /**
     * Validate output against quality gates
     */
    validateOutput(output: any): {
        valid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=AgentStateManager.d.ts.map