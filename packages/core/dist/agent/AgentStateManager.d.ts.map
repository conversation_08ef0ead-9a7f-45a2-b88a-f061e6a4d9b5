{"version": 3, "file": "AgentStateManager.d.ts", "sourceRoot": "", "sources": ["../../src/agent/AgentStateManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAIvD,MAAM,WAAW,UAAU;IACzB,GAAG,CAAC,EAAE,QAAQ,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,YAAY,CAAC;IAC7C,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB,KAAK,CAAC,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC7B,WAAW,CAAC,EAAE;YACZ,gBAAgB,CAAC,EAAE,MAAM,CAAC;YAC1B,aAAa,CAAC,EAAE,MAAM,CAAC;SACxB,CAAC;KACH,EAAE,CAAC;IACJ,YAAY,EAAE;QACZ,QAAQ,EAAE,MAAM,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;QACd,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB,CAAC;IACF,mBAAmB,CAAC,EAAE;QACpB,yBAAyB,CAAC,EAAE,MAAM,CAAC;QACnC,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,sBAAsB,CAAC,EAAE,MAAM,CAAC;KACjC,CAAC;CACH;AAGD,MAAM,WAAW,kBAAkB;IACjC,GAAG,CAAC,EAAE,QAAQ,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,OAAO,CAAC;IACnB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,OAAO,EAAE;QACP,aAAa,EAAE,MAAM,CAAC;QACtB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,eAAe,CAAC,EAAE,MAAM,CAAC;KAC1B,CAAC;IACF,UAAU,CAAC,EAAE;QACX,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,aAAa,CAAC,EAAE;QACd,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;QAC3B,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;KAC7B,CAAC;IACF,gBAAgB,CAAC,EAAE;QACjB,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;QACzB,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;QACzB,gBAAgB,CAAC,EAAE,OAAO,CAAC;KAC5B,CAAC;CACH;AAED,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,UAAU,CAAyB;IAC3C,OAAO,CAAC,WAAW,CAAiC;IACpD,OAAO,CAAC,UAAU,CAA2B;IAC7C,OAAO,CAAC,WAAW,CAAmC;gBAGpD,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,EAClC,WAAW,EAAE,UAAU,CAAC,kBAAkB,CAAC;IAM7C;;OAEG;IACG,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyB/C;;OAEG;IACH,QAAQ,IAAI,UAAU;IAOtB;;OAEG;IACH,gBAAgB,IAAI,kBAAkB,GAAG,IAAI;IAI7C;;OAEG;IACH,eAAe,IAAI,MAAM;IASzB;;OAEG;IACH,cAAc,IAAI,UAAU,CAAC,cAAc,CAAC;IAa5C;;OAEG;IACH,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC;IAI/B;;OAEG;IACH,eAAe,IAAI,MAAM,EAAE;IAI3B;;OAEG;IACH,qBAAqB,IAAI,UAAU,CAAC,qBAAqB,CAAC;IAI1D;;OAEG;IACH,QAAQ,IAAI,OAAO;IAInB;;OAEG;IACG,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB/D;;OAEG;IACG,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,YAAY,GAAG,YAAY,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC;IAqBxG;;OAEG;IACG,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,GAAG,YAAY,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAoClH;;OAEG;IACH,eAAe,IAAI,kBAAkB,CAAC,eAAe,CAAC;IAItD;;OAEG;IACH,kBAAkB,IAAI,kBAAkB,CAAC,kBAAkB,CAAC;IAI5D;;OAEG;IACH,cAAc,CAAC,MAAM,EAAE,GAAG,GAAG;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;CAyClE"}