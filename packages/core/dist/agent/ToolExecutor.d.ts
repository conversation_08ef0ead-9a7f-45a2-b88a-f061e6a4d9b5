import { ObjectId } from 'mongodb';
import { IDataStore } from '../persistance/IDataStore';
export interface ToolDefinition {
    _id?: ObjectId;
    tool_id: string;
    name: string;
    description: string;
    version: string;
    status: 'active' | 'inactive' | 'deprecated';
    created_at?: Date;
    updated_at?: Date;
    config?: {
        api_endpoint?: string;
        authentication?: {
            type: 'none' | 'api_key' | 'bearer_token' | 'oauth2';
            header_name?: string;
            token_prefix?: string;
        };
        default_parameters?: Record<string, any>;
        timeout_seconds?: number;
    };
    input_schema: Record<string, any>;
    output_schema: Record<string, any>;
    rate_limits?: {
        calls_per_minute?: number;
        calls_per_hour?: number;
        calls_per_day?: number;
        concurrent_calls?: number;
    };
    cost_model?: {
        cost_per_call?: number;
        cost_per_token?: number;
        currency?: string;
    };
    performance_stats?: {
        total_calls?: number;
        success_rate?: number;
        avg_response_time_ms?: number;
        avg_cost_per_call?: number;
        last_updated?: Date;
    };
}
export interface ToolExecutionLog {
    _id?: ObjectId;
    execution_id: string;
    tool_id: string;
    agent_id?: string;
    workflow_id?: string;
    executed_at: Date;
    input: Record<string, any>;
    output: Record<string, any>;
    performance: {
        execution_time_ms: number;
        success: boolean;
        cost?: number;
        tokens_consumed?: number;
        rate_limit_remaining?: number;
    };
    error?: {
        message: string;
        stack?: string;
        code?: string;
    } | null;
    retry_count?: number;
    embedding?: number[];
}
export interface ToolFunction {
    (input: Record<string, any>, context?: ToolExecutionContext): Promise<Record<string, any>>;
}
export interface ToolExecutionContext {
    agent_id?: string;
    workflow_id?: string;
    session_id?: string;
    trace_id?: string;
    credentials?: Record<string, any>;
    timeout_ms?: number;
}
export declare class ToolExecutor {
    private toolStore;
    private executionStore;
    private toolRegistry;
    private rateLimitTrackers;
    private toolCache;
    constructor(toolStore: IDataStore<ToolDefinition>, executionStore: IDataStore<ToolExecutionLog>);
    /**
     * Initialize built-in tools
     */
    private initializeBuiltInTools;
    /**
     * Register a tool function
     */
    registerTool(toolId: string, toolFunction: ToolFunction): void;
    /**
     * Get tool definition from database
     */
    getToolDefinition(toolId: string): Promise<ToolDefinition | null>;
    /**
     * Check rate limits for a tool
     */
    private checkRateLimit;
    /**
     * Update rate limit counters
     */
    private updateRateLimit;
    /**
     * Validate tool input against schema
     */
    private validateInput;
    /**
     * Execute a tool
     */
    execute(toolId: string, input: Record<string, any>, context?: ToolExecutionContext): Promise<Record<string, any>>;
    /**
     * Get remaining rate limit for a tool
     */
    private getRateLimitRemaining;
    /**
     * Update tool performance statistics
     */
    private updateToolStats;
    /**
     * Get tool execution history
     */
    getExecutionHistory(toolId: string, limit?: number): Promise<ToolExecutionLog[]>;
    /**
     * Get tools by capability
     */
    getToolsByCapability(capability: string): Promise<ToolDefinition[]>;
    /**
     * Create a new tool definition
     */
    createTool(toolData: Omit<ToolDefinition, '_id' | 'created_at' | 'updated_at'>): Promise<ToolDefinition>;
}
//# sourceMappingURL=ToolExecutor.d.ts.map