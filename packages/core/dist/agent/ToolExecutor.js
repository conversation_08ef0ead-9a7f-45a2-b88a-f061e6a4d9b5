"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutor = void 0;
const validator_1 = require("../schemas/validator");
const uuid_1 = require("uuid");
class ToolExecutor {
    constructor(toolStore, executionStore) {
        this.toolRegistry = new Map();
        this.rateLimitTrackers = new Map();
        this.toolCache = new Map();
        this.toolStore = toolStore;
        this.executionStore = executionStore;
        this.initializeBuiltInTools();
    }
    /**
     * Initialize built-in tools
     */
    initializeBuiltInTools() {
        // Tavily Search Tool
        this.registerTool('tavily_web_search', async (input) => {
            const { query, max_results = 5, search_depth = 'advanced' } = input;
            // Mock implementation - in production would call actual Tavily API
            console.log(`Searching Tavily for: ${query} (max_results: ${max_results}, depth: ${search_depth})`);
            return {
                results: [
                    {
                        title: `Search result for "${query}"`,
                        url: 'https://example.com/result1',
                        content: `This is a mock search result for the query: ${query}`,
                        score: 0.95,
                        published_date: new Date().toISOString()
                    }
                ],
                answer: `Based on the search for "${query}", here are the key findings...`,
                confidence: 0.87,
                total_results: 1
            };
        });
        // Internal Database Lookup Tool
        this.registerTool('internal_db_lookup', async (input) => {
            const { collection, filter, limit = 10 } = input;
            console.log(`Looking up in collection: ${collection} with filter:`, filter);
            return {
                results: [],
                count: 0,
                message: `Mock lookup in ${collection} collection`
            };
        });
        // Text Analysis Tool
        this.registerTool('text_analysis', async (input) => {
            const { text, analysis_type = 'sentiment' } = input;
            return {
                analysis_type,
                sentiment: 'positive',
                confidence: 0.85,
                key_phrases: ['example', 'analysis'],
                summary: `Analysis of text: ${text.substring(0, 50)}...`
            };
        });
    }
    /**
     * Register a tool function
     */
    registerTool(toolId, toolFunction) {
        this.toolRegistry.set(toolId, toolFunction);
    }
    /**
     * Get tool definition from database
     */
    async getToolDefinition(toolId) {
        // Check cache first
        if (this.toolCache.has(toolId)) {
            return this.toolCache.get(toolId);
        }
        // Load from database
        const tools = await this.toolStore.find({ tool_id: toolId, status: 'active' });
        if (tools.length === 0) {
            return null;
        }
        const tool = tools[0];
        this.toolCache.set(toolId, tool);
        return tool;
    }
    /**
     * Check rate limits for a tool
     */
    checkRateLimit(toolId, rateLimits) {
        if (!rateLimits)
            return true;
        const now = Date.now();
        const currentMinute = Math.floor(now / 60000);
        const currentHour = Math.floor(now / 3600000);
        const currentDay = Math.floor(now / 86400000);
        let tracker = this.rateLimitTrackers.get(toolId);
        if (!tracker) {
            tracker = {
                calls_this_minute: 0,
                calls_this_hour: 0,
                calls_this_day: 0,
                concurrent_calls: 0,
                last_reset_minute: currentMinute,
                last_reset_hour: currentHour,
                last_reset_day: currentDay
            };
            this.rateLimitTrackers.set(toolId, tracker);
        }
        // Reset counters if time periods have passed
        if (tracker.last_reset_minute < currentMinute) {
            tracker.calls_this_minute = 0;
            tracker.last_reset_minute = currentMinute;
        }
        if (tracker.last_reset_hour < currentHour) {
            tracker.calls_this_hour = 0;
            tracker.last_reset_hour = currentHour;
        }
        if (tracker.last_reset_day < currentDay) {
            tracker.calls_this_day = 0;
            tracker.last_reset_day = currentDay;
        }
        // Check limits
        if (rateLimits.calls_per_minute && tracker.calls_this_minute >= rateLimits.calls_per_minute) {
            return false;
        }
        if (rateLimits.calls_per_hour && tracker.calls_this_hour >= rateLimits.calls_per_hour) {
            return false;
        }
        if (rateLimits.calls_per_day && tracker.calls_this_day >= rateLimits.calls_per_day) {
            return false;
        }
        if (rateLimits.concurrent_calls && tracker.concurrent_calls >= rateLimits.concurrent_calls) {
            return false;
        }
        return true;
    }
    /**
     * Update rate limit counters
     */
    updateRateLimit(toolId) {
        const tracker = this.rateLimitTrackers.get(toolId);
        if (tracker) {
            tracker.calls_this_minute++;
            tracker.calls_this_hour++;
            tracker.calls_this_day++;
            tracker.concurrent_calls++;
        }
    }
    /**
     * Validate tool input against schema
     */
    validateInput(input, inputSchema) {
        try {
            // Simple validation - in production would use a proper JSON schema validator
            const errors = [];
            if (inputSchema.required) {
                for (const field of inputSchema.required) {
                    if (!(field in input)) {
                        errors.push(`Required field '${field}' is missing`);
                    }
                }
            }
            return { valid: errors.length === 0, errors };
        }
        catch (error) {
            return { valid: false, errors: [`Validation error: ${error.message}`] };
        }
    }
    /**
     * Execute a tool
     */
    async execute(toolId, input, context) {
        const executionId = (0, uuid_1.v4)();
        const startTime = Date.now();
        let success = false;
        let output = {};
        let error = null;
        let cost = 0;
        try {
            // Get tool definition
            const toolDef = await this.getToolDefinition(toolId);
            if (!toolDef) {
                throw new Error(`Tool '${toolId}' not found or inactive`);
            }
            // Check rate limits
            if (!this.checkRateLimit(toolId, toolDef.rate_limits)) {
                throw new Error(`Rate limit exceeded for tool '${toolId}'`);
            }
            // Validate input
            const validation = this.validateInput(input, toolDef.input_schema);
            if (!validation.valid) {
                throw new Error(`Input validation failed: ${validation.errors.join(', ')}`);
            }
            // Get tool function
            const toolFunction = this.toolRegistry.get(toolId);
            if (!toolFunction) {
                throw new Error(`Tool function for '${toolId}' not registered`);
            }
            // Update rate limits
            this.updateRateLimit(toolId);
            // Execute tool with timeout
            const timeoutMs = context?.timeout_ms ?? (toolDef.config?.timeout_seconds ? toolDef.config.timeout_seconds * 1000 : 30000);
            const executionPromise = toolFunction(input, context);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Tool execution timeout')), timeoutMs);
            });
            output = await Promise.race([executionPromise, timeoutPromise]);
            success = true;
            // Calculate cost
            if (toolDef.cost_model?.cost_per_call) {
                cost = toolDef.cost_model.cost_per_call;
            }
        }
        catch (e) {
            error = {
                message: e.message,
                stack: e.stack,
                code: e.code
            };
            output = { error: e.message };
        }
        finally {
            // Decrement concurrent calls counter
            const tracker = this.rateLimitTrackers.get(toolId);
            if (tracker) {
                tracker.concurrent_calls = Math.max(0, tracker.concurrent_calls - 1);
            }
            // Log execution
            const executionTime = Date.now() - startTime;
            const executionLog = {
                execution_id: executionId,
                tool_id: toolId,
                agent_id: context?.agent_id,
                workflow_id: context?.workflow_id,
                executed_at: new Date(),
                input,
                output,
                performance: {
                    execution_time_ms: executionTime,
                    success,
                    cost,
                    tokens_consumed: 0, // Would be calculated based on actual usage
                    rate_limit_remaining: this.getRateLimitRemaining(toolId)
                },
                error,
                retry_count: 0
            };
            // Validate and store execution log
            const validationData = {
                ...executionLog,
                executed_at: executionLog.executed_at.toISOString()
            };
            validator_1.SchemaValidator.validateOrThrow('toolExecutions', validationData);
            await this.executionStore.create(executionLog);
            // Update tool performance stats
            await this.updateToolStats(toolId, executionTime, success, cost);
        }
        if (!success && error) {
            throw new Error(error.message);
        }
        return output;
    }
    /**
     * Get remaining rate limit for a tool
     */
    getRateLimitRemaining(toolId) {
        const tracker = this.rateLimitTrackers.get(toolId);
        if (!tracker)
            return -1;
        // Return remaining calls for the most restrictive limit
        return Math.max(0, 100 - tracker.calls_this_minute); // Simplified
    }
    /**
     * Update tool performance statistics
     */
    async updateToolStats(toolId, executionTime, success, cost) {
        try {
            const toolDef = await this.getToolDefinition(toolId);
            if (!toolDef || !toolDef._id)
                return;
            const currentStats = toolDef.performance_stats || {
                total_calls: 0,
                success_rate: 1,
                avg_response_time_ms: 0,
                avg_cost_per_call: 0
            };
            const newTotalCalls = currentStats.total_calls + 1;
            const newSuccessRate = ((currentStats.success_rate * currentStats.total_calls) + (success ? 1 : 0)) / newTotalCalls;
            const newAvgResponseTime = ((currentStats.avg_response_time_ms * currentStats.total_calls) + executionTime) / newTotalCalls;
            const newAvgCost = ((currentStats.avg_cost_per_call * currentStats.total_calls) + cost) / newTotalCalls;
            await this.toolStore.update(toolDef._id.toString(), {
                performance_stats: {
                    total_calls: newTotalCalls,
                    success_rate: newSuccessRate,
                    avg_response_time_ms: newAvgResponseTime,
                    avg_cost_per_call: newAvgCost,
                    last_updated: new Date()
                }
            });
            // Update cache
            this.toolCache.delete(toolId);
        }
        catch (error) {
            console.warn(`Failed to update tool stats for ${toolId}:`, error);
        }
    }
    /**
     * Get tool execution history
     */
    async getExecutionHistory(toolId, limit = 10) {
        return await this.executionStore.find({ tool_id: toolId });
    }
    /**
     * Get tools by capability
     */
    async getToolsByCapability(capability) {
        // This would be enhanced with proper capability matching
        return await this.toolStore.find({ status: 'active' });
    }
    /**
     * Create a new tool definition
     */
    async createTool(toolData) {
        const now = new Date();
        const tool = {
            ...toolData,
            created_at: now,
            updated_at: now,
            performance_stats: {
                total_calls: 0,
                success_rate: 1,
                avg_response_time_ms: 0,
                avg_cost_per_call: 0,
                last_updated: now
            }
        };
        // Validate tool data
        const validationData = {
            ...tool,
            created_at: tool.created_at?.toISOString(),
            updated_at: tool.updated_at?.toISOString(),
            performance_stats: {
                ...tool.performance_stats,
                last_updated: tool.performance_stats?.last_updated?.toISOString()
            }
        };
        validator_1.SchemaValidator.validateOrThrow('agentTools', validationData);
        const created = await this.toolStore.create(tool);
        this.toolCache.set(tool.tool_id, created);
        return created;
    }
}
exports.ToolExecutor = ToolExecutor;
