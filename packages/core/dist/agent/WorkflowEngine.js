"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowEngine = void 0;
const validator_1 = require("../schemas/validator");
const uuid_1 = require("uuid");
class WorkflowEngine {
    constructor(workflowStore, agentStateManager, toolExecutor) {
        this.workflowStore = workflowStore;
        this.agentStateManager = agentStateManager;
        this.toolExecutor = toolExecutor;
    }
    /**
     * Create a new workflow
     */
    async createWorkflow(workflowName, steps, initialContext = {}, options = {}) {
        const workflow = {
            workflow_id: (0, uuid_1.v4)(),
            workflow_name: workflowName,
            status: 'pending',
            created_at: new Date(),
            updated_at: new Date(),
            workflow_definition: {
                name: workflowName,
                version: '1.0',
                steps
            },
            current_step: 0,
            execution_log: [],
            shared_context: initialContext,
            error_log: [],
            retry_attempts: 0,
            max_retries: options.max_retries || 3
        };
        // Validate workflow
        const validationData = {
            ...workflow,
            created_at: workflow.created_at.toISOString(),
            updated_at: workflow.updated_at.toISOString()
        };
        validator_1.SchemaValidator.validateOrThrow('agentWorkflows', validationData);
        const created = await this.workflowStore.create(workflow);
        return created;
    }
    /**
     * Execute a workflow
     */
    async executeWorkflow(workflowId, options = {}) {
        // Find workflow by workflow_id field, not _id
        const workflows = await this.workflowStore.find({ workflow_id: workflowId });
        if (workflows.length === 0) {
            throw new Error(`Workflow with ID '${workflowId}' not found.`);
        }
        const workflow = workflows[0];
        if (workflow.status === 'completed') {
            console.log(`Workflow ${workflowId} is already completed.`);
            return;
        }
        await this.updateWorkflowStatus(workflowId, 'in_progress');
        try {
            await this.executeWorkflowSequential(workflow, options);
            await this.updateWorkflowStatus(workflowId, 'completed');
            console.log(`✅ Workflow ${workflowId} completed successfully.`);
        }
        catch (error) {
            console.error(`❌ Workflow ${workflowId} failed:`, error);
            await this.updateWorkflowStatus(workflowId, 'failed');
            if (workflow.retry_attempts < workflow.max_retries) {
                console.log(`🔄 Retrying workflow ${workflowId} (attempt ${workflow.retry_attempts + 1}/${workflow.max_retries})`);
                await this.retryWorkflow(workflowId);
            }
        }
    }
    /**
     * Execute workflow steps sequentially
     */
    async executeWorkflowSequential(workflow, options) {
        const steps = workflow.workflow_definition.steps;
        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            // Check dependencies
            if (!this.areDependenciesMet(step, workflow.execution_log)) {
                console.log(`⏸️ Skipping step ${step.step_id} - dependencies not met`);
                continue;
            }
            // Check condition
            if (step.condition && !this.evaluateCondition(step.condition, workflow.shared_context)) {
                console.log(`⏸️ Skipping step ${step.step_id} - condition not met: ${step.condition}`);
                await this.logStepExecution(workflow, step, 'skipped');
                continue;
            }
            await this.executeStep(workflow, step, i);
        }
    }
    /**
     * Execute a single workflow step
     */
    async executeStep(workflow, step, stepIndex) {
        const startTime = new Date();
        let stepExecution = {
            step_id: step.step_id,
            agent_id: step.agent_id,
            status: 'in_progress',
            started_at: startTime,
            input: this.prepareStepInput(step, workflow.shared_context)
        };
        try {
            console.log(`🔄 Executing step ${step.step_id}: ${step.description}`);
            // Update current step
            await this.updateCurrentStep(workflow.workflow_id, stepIndex);
            // Load agent state
            await this.agentStateManager.loadState(step.agent_id);
            // Prepare execution context
            const context = {
                agent_id: step.agent_id,
                workflow_id: workflow.workflow_id,
                timeout_ms: (step.timeout_seconds || 30) * 1000
            };
            // Execute tool if specified
            let output = {};
            if (step.tool_id) {
                output = await this.toolExecutor.execute(step.tool_id, stepExecution.input, context);
            }
            else {
                // Custom step execution logic could go here
                output = { message: `Step ${step.step_id} executed without tool` };
            }
            // Update shared context
            workflow.shared_context[step.step_id] = output;
            await this.updateWorkflowContext(workflow.workflow_id, workflow.shared_context);
            // Complete step execution
            const endTime = new Date();
            stepExecution = {
                ...stepExecution,
                status: 'completed',
                completed_at: endTime,
                duration_seconds: (endTime.getTime() - startTime.getTime()) / 1000,
                output,
                cost: output.cost || 0,
                tokens_used: output.tokens_used || 0
            };
            await this.logStepExecution(workflow, step, 'completed', stepExecution);
            console.log(`✅ Step ${step.step_id} completed successfully`);
        }
        catch (error) {
            const endTime = new Date();
            stepExecution = {
                ...stepExecution,
                status: 'failed',
                completed_at: endTime,
                duration_seconds: (endTime.getTime() - startTime.getTime()) / 1000,
                error: {
                    message: error.message,
                    stack: error.stack,
                    retry_count: 0
                }
            };
            await this.logStepExecution(workflow, step, 'failed', stepExecution);
            await this.logError(workflow.workflow_id, step.step_id, error.message);
            // Retry logic
            const maxRetries = step.retry_count || 2;
            if (stepExecution.error.retry_count < maxRetries) {
                console.log(`🔄 Retrying step ${step.step_id} (attempt ${stepExecution.error.retry_count + 1}/${maxRetries})`);
                stepExecution.error.retry_count++;
                await this.executeStep(workflow, step, stepIndex);
                return;
            }
            console.error(`❌ Step ${step.step_id} failed after ${maxRetries} retries:`, error);
            throw error;
        }
    }
    /**
     * Prepare input for a step based on input mapping
     */
    prepareStepInput(step, sharedContext) {
        if (!step.input_mapping) {
            return {};
        }
        const input = {};
        for (const [key, mapping] of Object.entries(step.input_mapping)) {
            if (typeof mapping === 'string') {
                // Simple context reference
                if (mapping.startsWith('context.')) {
                    const contextKey = mapping.substring(8);
                    input[key] = sharedContext[contextKey];
                }
                else {
                    input[key] = mapping;
                }
            }
            else {
                input[key] = mapping;
            }
        }
        return input;
    }
    /**
     * Check if step dependencies are met
     */
    areDependenciesMet(step, executionLog) {
        if (!step.depends_on || step.depends_on.length === 0) {
            return true;
        }
        return step.depends_on.every(depStepId => {
            const depExecution = executionLog.find(log => log.step_id === depStepId);
            return depExecution && depExecution.status === 'completed';
        });
    }
    /**
     * Evaluate a simple condition
     */
    evaluateCondition(condition, context) {
        try {
            // Simple condition evaluation - in production would use a proper expression engine
            if (condition.includes('exists')) {
                const key = condition.match(/exists\(([^)]+)\)/)?.[1];
                return key ? context[key] !== undefined : false;
            }
            if (condition.includes('equals')) {
                const match = condition.match(/equals\(([^,]+),\s*([^)]+)\)/);
                if (match) {
                    const [, key, value] = match;
                    return context[key] === value.replace(/['"]/g, '');
                }
            }
            return true; // Default to true for unknown conditions
        }
        catch (error) {
            console.warn(`Failed to evaluate condition: ${condition}`, error);
            return false;
        }
    }
    /**
     * Log step execution
     */
    async logStepExecution(workflow, step, status, execution) {
        const stepExecution = {
            step_id: step.step_id,
            agent_id: step.agent_id,
            status,
            ...execution
        };
        // Update execution log
        const updatedLog = [...workflow.execution_log];
        const existingIndex = updatedLog.findIndex(log => log.step_id === step.step_id);
        if (existingIndex >= 0) {
            updatedLog[existingIndex] = stepExecution;
        }
        else {
            updatedLog.push(stepExecution);
        }
        await this.workflowStore.update(workflow.workflow_id, {
            execution_log: updatedLog,
            updated_at: new Date()
        });
    }
    /**
     * Log workflow error
     */
    async logError(workflowId, stepId, error) {
        const workflow = await this.workflowStore.findById(workflowId);
        if (!workflow)
            return;
        const errorLog = [...workflow.error_log, {
                step_id: stepId,
                error,
                timestamp: new Date()
            }];
        await this.workflowStore.update(workflowId, {
            error_log: errorLog,
            updated_at: new Date()
        });
    }
    /**
     * Update workflow status
     */
    async updateWorkflowStatus(workflowId, status) {
        const workflows = await this.workflowStore.find({ workflow_id: workflowId });
        if (workflows.length > 0 && workflows[0]._id) {
            await this.workflowStore.update(workflows[0]._id.toString(), {
                status,
                updated_at: new Date()
            });
        }
    }
    /**
     * Update current step
     */
    async updateCurrentStep(workflowId, stepIndex) {
        const workflows = await this.workflowStore.find({ workflow_id: workflowId });
        if (workflows.length > 0 && workflows[0]._id) {
            await this.workflowStore.update(workflows[0]._id.toString(), {
                current_step: stepIndex,
                updated_at: new Date()
            });
        }
    }
    /**
     * Update workflow context
     */
    async updateWorkflowContext(workflowId, context) {
        const workflows = await this.workflowStore.find({ workflow_id: workflowId });
        if (workflows.length > 0 && workflows[0]._id) {
            await this.workflowStore.update(workflows[0]._id.toString(), {
                shared_context: context,
                updated_at: new Date()
            });
        }
    }
    /**
     * Retry a failed workflow
     */
    async retryWorkflow(workflowId) {
        const workflow = await this.workflowStore.findById(workflowId);
        if (!workflow) {
            throw new Error(`Workflow with ID '${workflowId}' not found.`);
        }
        // Increment retry attempts
        await this.workflowStore.update(workflowId, {
            retry_attempts: (workflow.retry_attempts || 0) + 1,
            status: 'pending',
            updated_at: new Date()
        });
        // Re-execute workflow
        await this.executeWorkflow(workflowId);
    }
    /**
     * Cancel a workflow
     */
    async cancelWorkflow(workflowId) {
        await this.updateWorkflowStatus(workflowId, 'cancelled');
        console.log(`🛑 Workflow ${workflowId} cancelled`);
    }
    /**
     * Get workflow status
     */
    async getWorkflowStatus(workflowId) {
        return await this.workflowStore.findById(workflowId);
    }
    /**
     * Get workflow execution summary
     */
    async getWorkflowSummary(workflowId) {
        const workflow = await this.workflowStore.findById(workflowId);
        if (!workflow)
            return null;
        const totalSteps = workflow.workflow_definition.steps.length;
        const completedSteps = workflow.execution_log.filter(log => log.status === 'completed').length;
        const failedSteps = workflow.execution_log.filter(log => log.status === 'failed').length;
        const totalCost = workflow.execution_log.reduce((sum, log) => sum + (log.cost || 0), 0);
        const totalDuration = workflow.execution_log.reduce((sum, log) => sum + (log.duration_seconds || 0), 0);
        return {
            workflow,
            totalSteps,
            completedSteps,
            failedSteps,
            totalCost,
            totalDuration
        };
    }
}
exports.WorkflowEngine = WorkflowEngine;
