"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentStateManager = void 0;
const validator_1 = require("../schemas/validator");
class AgentStateManager {
    constructor(agentStore, configStore) {
        this.agentState = null;
        this.agentConfig = null;
        this.agentStore = agentStore;
        this.configStore = configStore;
    }
    /**
     * Load agent state and active configuration
     */
    async loadState(agentId) {
        // Load agent definition
        const agents = await this.agentStore.find({ agent_id: agentId });
        if (agents.length === 0) {
            throw new Error(`Agent with ID '${agentId}' not found.`);
        }
        if (agents.length > 1) {
            console.warn(`Multiple agents found with ID '${agentId}'. Using the first one.`);
        }
        this.agentState = agents[0];
        // Load active configuration
        const configs = await this.configStore.find({
            agent_id: agentId,
            is_active: true
        });
        if (configs.length > 0) {
            if (configs.length > 1) {
                console.warn(`Multiple active configs found for agent '${agentId}'. Using the first one.`);
            }
            this.agentConfig = configs[0];
        }
    }
    /**
     * Get the current agent state
     */
    getState() {
        if (!this.agentState) {
            throw new Error('Agent state has not been loaded. Call loadState() first.');
        }
        return this.agentState;
    }
    /**
     * Get the current agent configuration
     */
    getConfiguration() {
        return this.agentConfig;
    }
    /**
     * Get the effective system prompt (from config if available, otherwise from agent)
     */
    getSystemPrompt() {
        if (this.agentConfig?.prompts?.system_prompt) {
            return this.agentConfig.prompts.system_prompt;
        }
        const state = this.getState();
        return state.model_config.system_prompt || 'You are a helpful AI assistant.';
    }
    /**
     * Get effective model configuration
     */
    getModelConfig() {
        const state = this.getState();
        const config = this.agentConfig;
        return {
            provider: state.model_config.provider,
            model: state.model_config.model,
            temperature: config?.parameters?.temperature ?? state.model_config.temperature ?? 0.7,
            max_tokens: config?.parameters?.max_tokens ?? state.model_config.max_tokens ?? 2000,
            system_prompt: this.getSystemPrompt()
        };
    }
    /**
     * Get agent tools
     */
    getTools() {
        return this.getState().tools || [];
    }
    /**
     * Get agent capabilities
     */
    getCapabilities() {
        return this.getState().capabilities || [];
    }
    /**
     * Get performance targets
     */
    getPerformanceTargets() {
        return this.getState().performance_targets;
    }
    /**
     * Check if agent is active
     */
    isActive() {
        return this.getState().status === 'active';
    }
    /**
     * Update agent status
     */
    async updateStatus(status) {
        const state = this.getState();
        if (!state._id) {
            throw new Error('Cannot update agent without ID');
        }
        const updated = await this.agentStore.update(state._id.toString(), {
            status,
            updated_at: new Date()
        });
        if (updated) {
            this.agentState = updated;
        }
    }
    /**
     * Create a new agent
     */
    async createAgent(agentData) {
        const now = new Date();
        const agent = {
            ...agentData,
            created_at: now,
            updated_at: now
        };
        // Validate agent data
        const validationData = {
            ...agent,
            created_at: agent.created_at?.toISOString(),
            updated_at: agent.updated_at?.toISOString()
        };
        validator_1.SchemaValidator.validateOrThrow('agent', validationData);
        const created = await this.agentStore.create(agent);
        this.agentState = created;
        return created;
    }
    /**
     * Create a new agent configuration
     */
    async createConfiguration(configData) {
        const config = {
            ...configData,
            created_at: new Date()
        };
        // Validate configuration data
        const validationData = {
            ...config,
            created_at: config.created_at?.toISOString()
        };
        validator_1.SchemaValidator.validateOrThrow('agentConfigurations', validationData);
        // Deactivate other configurations for this agent if this one is active
        if (config.is_active) {
            const existingConfigs = await this.configStore.find({
                agent_id: config.agent_id,
                is_active: true
            });
            for (const existingConfig of existingConfigs) {
                if (existingConfig._id) {
                    await this.configStore.update(existingConfig._id.toString(), { is_active: false });
                }
            }
        }
        const created = await this.configStore.create(config);
        if (config.is_active) {
            this.agentConfig = created;
        }
        return created;
    }
    /**
     * Get quality gates for validation
     */
    getQualityGates() {
        return this.agentConfig?.quality_gates;
    }
    /**
     * Get network policies for security
     */
    getNetworkPolicies() {
        return this.agentConfig?.network_policies;
    }
    /**
     * Validate output against quality gates
     */
    validateOutput(output) {
        const qualityGates = this.getQualityGates();
        if (!qualityGates) {
            return { valid: true, errors: [] };
        }
        const errors = [];
        // Check minimum confidence
        if (qualityGates.min_confidence && output.confidence < qualityGates.min_confidence) {
            errors.push(`Confidence ${output.confidence} below minimum ${qualityGates.min_confidence}`);
        }
        // Check required fields
        if (qualityGates.required_fields) {
            for (const field of qualityGates.required_fields) {
                if (!output[field]) {
                    errors.push(`Required field '${field}' is missing`);
                }
            }
        }
        // Check validation rules (simplified - in production would use a rule engine)
        if (qualityGates.validation_rules) {
            for (const rule of qualityGates.validation_rules) {
                try {
                    // Simple rule evaluation - in production would use a proper rule engine
                    if (rule.includes('length >') && typeof output.content === 'string') {
                        const minLength = parseInt(rule.match(/length > (\d+)/)?.[1] || '0');
                        if (output.content.length <= minLength) {
                            errors.push(`Content length ${output.content.length} violates rule: ${rule}`);
                        }
                    }
                }
                catch (error) {
                    console.warn(`Failed to evaluate rule: ${rule}`, error);
                }
            }
        }
        return { valid: errors.length === 0, errors };
    }
}
exports.AgentStateManager = AgentStateManager;
