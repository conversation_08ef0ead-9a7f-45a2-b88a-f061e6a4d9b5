"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoMemoryProvider = void 0;
const validator_1 = require("../schemas/validator");
const MEMORY_COLLECTION = 'agent_working_memory';
class MongoMemoryProvider {
    constructor(db, ttlHours = 3) {
        this.collection = db.collection(MEMORY_COLLECTION);
        this.ttlHours = ttlHours;
    }
    async getHistory(agentId, sessionId) {
        const memory = await this.collection.findOne({
            agent_id: agentId,
            session_id: sessionId
        });
        if (!memory) {
            return [];
        }
        // Convert ContextMessage to ChatMessage format
        return memory.context_window.map(msg => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp
        }));
    }
    async addMessage(agentId, sessionId, message) {
        const ttlMs = this.ttlHours * 60 * 60 * 1000;
        const now = new Date();
        const expires_at = new Date(now.getTime() + ttlMs);
        const contextMessage = {
            role: message.role,
            content: message.content,
            timestamp: message.timestamp || now
        };
        // Try to update existing document
        const result = await this.collection.updateOne({ agent_id: agentId, session_id: sessionId }, {
            $push: { context_window: contextMessage },
            $set: { expires_at },
        });
        // If no document exists, create a new one
        if (result.matchedCount === 0) {
            const newDoc = {
                session_id: sessionId,
                agent_id: agentId,
                created_at: now,
                expires_at,
                context_window: [contextMessage],
                working_state: {
                    current_task: 'conversation',
                    progress: 0,
                    confidence: 1.0,
                    variables: {}
                },
                temp_findings: {}
            };
            // Convert dates to ISO strings for validation
            const validationDoc = {
                ...newDoc,
                created_at: newDoc.created_at.toISOString(),
                expires_at: newDoc.expires_at.toISOString(),
                context_window: newDoc.context_window.map(msg => ({
                    ...msg,
                    timestamp: msg.timestamp.toISOString()
                }))
            };
            // Validate the document
            validator_1.SchemaValidator.validateOrThrow('agentWorkingMemory', validationDoc);
            await this.collection.insertOne(newDoc);
        }
    }
    /**
     * Get the current working state for an agent session
     */
    async getWorkingState(agentId, sessionId) {
        const memory = await this.collection.findOne({ agent_id: agentId, session_id: sessionId }, { projection: { working_state: 1 } });
        return memory?.working_state || null;
    }
    /**
     * Update the working state for an agent session
     */
    async updateWorkingState(agentId, sessionId, state) {
        const ttlMs = this.ttlHours * 60 * 60 * 1000;
        const expires_at = new Date(Date.now() + ttlMs);
        await this.collection.updateOne({ agent_id: agentId, session_id: sessionId }, {
            $set: {
                working_state: state,
                expires_at
            }
        }, { upsert: true });
    }
    /**
     * Get temporary findings for an agent session
     */
    async getTempFindings(agentId, sessionId) {
        const memory = await this.collection.findOne({ agent_id: agentId, session_id: sessionId }, { projection: { temp_findings: 1 } });
        return memory?.temp_findings || null;
    }
    /**
     * Update temporary findings for an agent session
     */
    async updateTempFindings(agentId, sessionId, findings) {
        const ttlMs = this.ttlHours * 60 * 60 * 1000;
        const expires_at = new Date(Date.now() + ttlMs);
        await this.collection.updateOne({ agent_id: agentId, session_id: sessionId }, {
            $set: {
                temp_findings: findings,
                expires_at
            }
        }, { upsert: true });
    }
    /**
     * Clear all working memory for an agent session
     */
    async clearSession(agentId, sessionId) {
        await this.collection.deleteOne({ agent_id: agentId, session_id: sessionId });
    }
    /**
     * Get all active sessions for an agent
     */
    async getActiveSessions(agentId) {
        const sessions = await this.collection.find({ agent_id: agentId }, { projection: { session_id: 1 } }).toArray();
        return sessions.map(s => s.session_id);
    }
}
exports.MongoMemoryProvider = MongoMemoryProvider;
