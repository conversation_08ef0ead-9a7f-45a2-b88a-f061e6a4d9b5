"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoEmbeddingProvider = void 0;
const validator_1 = require("../schemas/validator");
class MongoEmbeddingProvider {
    constructor(db, collectionName = 'vector_embeddings', indexName = 'vector_search_index') {
        this.collection = db.collection(collectionName);
        this.indexName = indexName;
    }
    async upsert(vectors) {
        const operations = vectors.map(vector => {
            // Convert Vector to VectorEmbeddingDocument format
            const document = {
                embedding_id: vector.id,
                source_type: vector.metadata?.source_type || 'unknown',
                source_id: vector.metadata?.source_id || vector.id,
                agent_id: vector.metadata?.agent_id,
                created_at: new Date(),
                embedding: {
                    values: vector.values,
                    meta: {
                        provider: vector.metadata?.provider || 'unknown',
                        model: vector.metadata?.model || 'unknown',
                        version: vector.metadata?.version || '1.0'
                    }
                },
                content: {
                    text: vector.metadata?.text || '',
                    summary: vector.metadata?.summary
                },
                metadata: vector.metadata,
                usage_stats: {
                    similarity_searches: 0,
                    avg_similarity_score: 0
                }
            };
            // Validate the document
            validator_1.SchemaValidator.validateOrThrow('vectorEmbeddings', document);
            return {
                updateOne: {
                    filter: { embedding_id: vector.id },
                    update: {
                        $set: document,
                        $inc: { 'usage_stats.similarity_searches': 0 }
                    },
                    upsert: true,
                },
            };
        });
        await this.collection.bulkWrite(operations);
    }
    async query(vector, topK, filter) {
        const pipeline = [
            {
                $vectorSearch: {
                    index: this.indexName,
                    path: 'embedding.values',
                    queryVector: vector,
                    numCandidates: Math.max(topK * 10, 150),
                    limit: topK,
                    filter: filter,
                },
            },
            {
                $addFields: {
                    score: { $meta: 'vectorSearchScore' },
                },
            },
            {
                $project: {
                    embedding_id: 1,
                    'embedding.values': 1,
                    metadata: 1,
                    content: 1,
                    score: 1,
                },
            },
        ];
        const results = await this.collection.aggregate(pipeline).toArray();
        // Update usage statistics
        const embeddingIds = results.map(r => r.embedding_id);
        if (embeddingIds.length > 0) {
            await this.collection.updateMany({ embedding_id: { $in: embeddingIds } }, {
                $inc: { 'usage_stats.similarity_searches': 1 },
                $set: { 'usage_stats.last_matched': new Date() }
            });
        }
        // Convert back to Vector format
        return results.map(doc => ({
            id: doc.embedding_id,
            values: doc.embedding.values,
            metadata: {
                ...doc.metadata,
                score: doc.score,
                text: doc.content?.text,
                summary: doc.content?.summary
            }
        }));
    }
    /**
     * Hybrid search combining vector and text search
     */
    async hybridSearch(query, queryVector, topK, vectorWeight = 0.7, textWeight = 0.3, filter) {
        const pipeline = [
            // Stage 1: Vector similarity search
            {
                $vectorSearch: {
                    index: this.indexName,
                    path: 'embedding.values',
                    queryVector: queryVector,
                    numCandidates: Math.max(topK * 10, 150),
                    limit: Math.max(topK * 2, 50),
                    filter: filter,
                },
            },
            {
                $addFields: {
                    vector_score: { $meta: 'vectorSearchScore' },
                },
            },
            // Stage 2: Text search
            {
                $search: {
                    index: 'text_search_index',
                    compound: {
                        must: [
                            {
                                text: {
                                    query: query,
                                    path: ['content.text', 'content.summary'],
                                },
                            },
                        ],
                        filter: filter ? [filter] : [],
                    },
                },
            },
            {
                $addFields: {
                    text_score: { $meta: 'searchScore' },
                },
            },
            // Stage 3: Combine scores
            {
                $addFields: {
                    combined_score: {
                        $add: [
                            { $multiply: ['$vector_score', vectorWeight] },
                            { $multiply: ['$text_score', textWeight] },
                        ],
                    },
                },
            },
            // Stage 4: Sort and limit
            { $sort: { combined_score: -1 } },
            { $limit: topK },
            {
                $project: {
                    embedding_id: 1,
                    'embedding.values': 1,
                    metadata: 1,
                    content: 1,
                    vector_score: 1,
                    text_score: 1,
                    combined_score: 1,
                },
            },
        ];
        const results = await this.collection.aggregate(pipeline).toArray();
        return results.map(doc => ({
            id: doc.embedding_id,
            values: doc.embedding.values,
            metadata: {
                ...doc.metadata,
                vector_score: doc.vector_score,
                text_score: doc.text_score,
                combined_score: doc.combined_score,
                text: doc.content?.text,
                summary: doc.content?.summary
            }
        }));
    }
}
exports.MongoEmbeddingProvider = MongoEmbeddingProvider;
