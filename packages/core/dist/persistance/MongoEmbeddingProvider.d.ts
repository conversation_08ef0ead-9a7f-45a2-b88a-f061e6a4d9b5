import { Db, ObjectId } from 'mongodb';
import { IEmbeddingStore, Vector, QueryFilter } from './IEmbeddingStore';
export interface VectorEmbeddingDocument {
    _id?: ObjectId;
    embedding_id: string;
    source_type: string;
    source_id: string;
    agent_id?: string;
    created_at: Date;
    embedding: {
        values: number[];
        meta: {
            provider: string;
            model: string;
            version: string;
        };
    };
    content: {
        text: string;
        summary?: string;
    };
    metadata?: Record<string, any>;
    usage_stats?: {
        similarity_searches: number;
        last_matched?: Date;
        avg_similarity_score?: number;
    };
}
export declare class MongoEmbeddingProvider implements IEmbeddingStore {
    private collection;
    private indexName;
    constructor(db: Db, collectionName?: string, indexName?: string);
    upsert(vectors: Vector[]): Promise<void>;
    query(vector: number[], topK: number, filter?: QueryFilter): Promise<Vector[]>;
    /**
     * Hybrid search combining vector and text search
     */
    hybridSearch(query: string, queryVector: number[], topK: number, vectorWeight?: number, textWeight?: number, filter?: QueryFilter): Promise<Vector[]>;
}
//# sourceMappingURL=MongoEmbeddingProvider.d.ts.map