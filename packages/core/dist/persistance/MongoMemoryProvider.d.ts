import { Db } from 'mongodb';
import { IMemoryStore, ChatMessage } from './IMemoryStore';
interface WorkingState {
    current_task?: string;
    progress?: number;
    next_action?: string;
    confidence?: number;
    variables?: Record<string, any>;
}
export declare class MongoMemoryProvider implements IMemoryStore {
    private collection;
    private ttlHours;
    constructor(db: Db, ttlHours?: number);
    getHistory(agentId: string, sessionId: string): Promise<ChatMessage[]>;
    addMessage(agentId: string, sessionId: string, message: ChatMessage): Promise<void>;
    /**
     * Get the current working state for an agent session
     */
    getWorkingState(agentId: string, sessionId: string): Promise<WorkingState | null>;
    /**
     * Update the working state for an agent session
     */
    updateWorkingState(agentId: string, sessionId: string, state: Partial<WorkingState>): Promise<void>;
    /**
     * Get temporary findings for an agent session
     */
    getTempFindings(agentId: string, sessionId: string): Promise<Record<string, any> | null>;
    /**
     * Update temporary findings for an agent session
     */
    updateTempFindings(agentId: string, sessionId: string, findings: Record<string, any>): Promise<void>;
    /**
     * Clear all working memory for an agent session
     */
    clearSession(agentId: string, sessionId: string): Promise<void>;
    /**
     * Get all active sessions for an agent
     */
    getActiveSessions(agentId: string): Promise<string[]>;
}
export {};
//# sourceMappingURL=MongoMemoryProvider.d.ts.map