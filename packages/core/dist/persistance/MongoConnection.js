"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoConnection = void 0;
const mongodb_1 = require("mongodb");
class MongoConnection {
    constructor(uri, dbName) {
        this.client = new mongodb_1.MongoClient(uri);
        this.db = this.client.db(dbName);
    }
    static getInstance(uri, dbName) {
        if (!MongoConnection.instance) {
            MongoConnection.instance = new MongoConnection(uri, dbName);
        }
        return MongoConnection.instance;
    }
    async connect() {
        try {
            // The connect() method will no-op if already connected.
            await this.client.connect();
        }
        catch (error) {
            // The driver will throw an error if it's already connected or connecting.
            // We can safely ignore this for the purpose of ensuring a connection.
            if (error instanceof Error && error.name !== 'MongoServerSelectionError') {
                console.error("Connection error:", error);
            }
        }
    }
    async disconnect() {
        await this.client.close();
    }
    getDb() {
        return this.db;
    }
}
exports.MongoConnection = MongoConnection;
