{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Trace", "description": "Captures the detailed, step-by-step reasoning trace of an agent for a specific task.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "trace_id": {"type": "string"}, "workflow_id": {"type": "string"}, "plan_id": {"type": "string"}, "agent_id": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "trace_data": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["observation", "thought", "action", "tool_output"]}, "content": {"type": ["string", "object"]}, "tool": {"type": "string"}, "input": {"type": "object"}}, "required": ["type"]}}}, "required": ["trace_id", "agent_id", "timestamp", "trace_data"]}