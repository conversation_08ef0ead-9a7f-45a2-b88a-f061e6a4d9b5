{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ToolExecution", "description": "Logs the execution of a single tool by an agent.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "execution_id": {"type": "string"}, "tool_id": {"type": "string"}, "agent_id": {"type": "string"}, "workflow_id": {"type": "string"}, "executed_at": {"type": "string", "format": "date-time"}, "input": {"type": "object"}, "output": {"type": "object"}, "performance": {"type": "object", "properties": {"execution_time_ms": {"type": "integer"}, "success": {"type": "boolean"}, "cost": {"type": "number"}}, "required": ["execution_time_ms", "success"]}, "error": {"type": "object", "properties": {"message": {"type": "string"}, "stack": {"type": "string"}}}}, "required": ["execution_id", "tool_id", "agent_id", "executed_at", "input", "output", "performance"]}