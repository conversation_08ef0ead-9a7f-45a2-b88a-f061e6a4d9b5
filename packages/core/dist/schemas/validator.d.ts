export interface ValidationResult {
    valid: boolean;
    errors?: string[];
}
export declare class SchemaValidator {
    /**
     * Validates data against a specific schema
     */
    static validate(schemaName: string, data: any): ValidationResult;
    /**
     * Validates and throws an error if validation fails
     */
    static validateOrThrow(schemaName: string, data: any): void;
    /**
     * Gets all available schema names
     */
    static getAvailableSchemas(): string[];
}
export declare const validateAgent: (data: any) => ValidationResult;
export declare const validateAgentConfiguration: (data: any) => ValidationResult;
export declare const validateAgentMemory: (data: any) => ValidationResult;
export declare const validateAgentWorkingMemory: (data: any) => ValidationResult;
export declare const validateAgentTools: (data: any) => ValidationResult;
export declare const validateAgentPerformanceMetrics: (data: any) => ValidationResult;
export declare const validateAgentWorkflows: (data: any) => ValidationResult;
export declare const validateVectorEmbeddings: (data: any) => ValidationResult;
export declare const validateToolExecutions: (data: any) => ValidationResult;
export declare const validateTraces: (data: any) => ValidationResult;
export declare const validateDynamicPlans: (data: any) => ValidationResult;
export declare const validateEvaluations: (data: any) => ValidationResult;
export declare const validateHumanFeedback: (data: any) => ValidationResult;
export declare const validateAgentPermissions: (data: any) => ValidationResult;
export declare const validateResourceRegistry: (data: any) => ValidationResult;
export declare const validateSecureCredentials: (data: any) => ValidationResult;
export declare const validateIngestionPipelines: (data: any) => ValidationResult;
export declare const validateAgentEvents: (data: any) => ValidationResult;
//# sourceMappingURL=validator.d.ts.map