{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentWorkflow", "description": "Defines a multi-step workflow to be executed by one or more agents.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "failed", "cancelled"]}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "current_step": {"type": "integer"}, "workflow_definition": {"type": "object", "properties": {"steps": {"type": "array", "items": {"type": "object", "properties": {"step_id": {"type": "string"}, "agent_id": {"type": "string"}, "description": {"type": "string"}, "depends_on": {"type": "array", "items": {"type": "string"}}, "timeout_seconds": {"type": "integer"}, "retry_count": {"type": "integer"}}, "required": ["step_id", "agent_id"]}}}, "required": ["steps"]}, "execution_log": {"type": "array", "items": {"type": "object", "properties": {"step_id": {"type": "string"}, "status": {"type": "string"}, "started_at": {"type": "string", "format": "date-time"}, "completed_at": {"type": "string", "format": "date-time"}, "output": {"type": "object"}}}}, "shared_context": {"type": "object"}}, "required": ["workflow_id", "status", "workflow_definition"]}