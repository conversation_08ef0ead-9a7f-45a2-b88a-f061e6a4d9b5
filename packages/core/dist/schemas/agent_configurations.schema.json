{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentConfiguration", "description": "Dynamic configuration for an AI agent, supporting A/B testing and behavioral parameters.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "config_id": {"type": "string", "description": "Unique identifier for this configuration version."}, "agent_id": {"type": "string", "description": "Reference to the agent this configuration applies to."}, "version": {"type": "string", "description": "Configuration version for A/B testing."}, "is_active": {"type": "boolean", "description": "Whether this configuration is currently active."}, "created_at": {"type": "string", "format": "date-time"}, "prompts": {"type": "object", "description": "Dynamic prompts that can be A/B tested.", "properties": {"system_prompt": {"type": "string"}, "task_prompt": {"type": "string"}, "analysis_prompt": {"type": "string"}, "fallback_prompt": {"type": "string"}}, "required": ["system_prompt"]}, "parameters": {"type": "object", "description": "Behavioral parameters for the agent.", "properties": {"confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1}, "max_iterations": {"type": "integer", "minimum": 1}, "temperature": {"type": "number", "minimum": 0, "maximum": 2}, "max_tokens": {"type": "integer", "minimum": 1}}}, "quality_gates": {"type": "object", "description": "Quality control rules for agent outputs.", "properties": {"min_confidence": {"type": "number", "minimum": 0, "maximum": 1}, "required_fields": {"type": "array", "items": {"type": "string"}}, "validation_rules": {"type": "array", "items": {"type": "string"}}}}, "network_policies": {"type": "object", "description": "Network egress policies for security.", "properties": {"allowed_hosts": {"type": "array", "items": {"type": "string"}}, "blocked_hosts": {"type": "array", "items": {"type": "string"}}, "require_approval": {"type": "boolean"}}}}, "required": ["config_id", "agent_id", "version", "is_active", "prompts"]}