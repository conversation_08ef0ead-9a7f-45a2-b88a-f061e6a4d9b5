{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentPerformanceMetrics", "description": "Performance metrics for an AI agent over a specific time window.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "metric_id": {"type": "string", "description": "Unique identifier for this metrics record."}, "agent_id": {"type": "string", "description": "Agent these metrics apply to."}, "timestamp": {"type": "string", "format": "date-time", "description": "When these metrics were recorded."}, "time_window": {"type": "string", "enum": ["1_minute", "5_minutes", "15_minutes", "1_hour", "1_day"], "description": "Time window these metrics cover."}, "metrics": {"type": "object", "description": "Core performance metrics.", "properties": {"tasks_completed": {"type": "integer", "minimum": 0}, "tasks_failed": {"type": "integer", "minimum": 0}, "success_rate": {"type": "number", "minimum": 0, "maximum": 1}, "avg_response_time_seconds": {"type": "number", "minimum": 0}, "median_response_time_seconds": {"type": "number", "minimum": 0}, "p95_response_time_seconds": {"type": "number", "minimum": 0}, "total_cost_usd": {"type": "number", "minimum": 0}, "avg_cost_per_task": {"type": "number", "minimum": 0}, "total_tokens_used": {"type": "integer", "minimum": 0}, "avg_tokens_per_task": {"type": "integer", "minimum": 0}}, "required": ["tasks_completed", "tasks_failed", "success_rate"]}, "quality": {"type": "object", "description": "Quality metrics for agent outputs.", "properties": {"avg_confidence_score": {"type": "number", "minimum": 0, "maximum": 1}, "min_confidence_score": {"type": "number", "minimum": 0, "maximum": 1}, "max_confidence_score": {"type": "number", "minimum": 0, "maximum": 1}, "user_satisfaction_score": {"type": "number", "minimum": 1, "maximum": 5}, "data_completeness_rate": {"type": "number", "minimum": 0, "maximum": 1}}}, "resources": {"type": "object", "description": "Resource usage metrics.", "properties": {"memory_usage_mb": {"type": "number", "minimum": 0}, "cpu_usage_percent": {"type": "number", "minimum": 0, "maximum": 100}, "network_requests": {"type": "integer", "minimum": 0}, "cache_hit_rate": {"type": "number", "minimum": 0, "maximum": 1}}}, "errors": {"type": "object", "description": "Error analysis.", "properties": {"total_errors": {"type": "integer", "minimum": 0}, "error_types": {"type": "object", "additionalProperties": {"type": "integer", "minimum": 0}}, "error_rate": {"type": "number", "minimum": 0, "maximum": 1}}}}, "required": ["metric_id", "agent_id", "timestamp", "time_window", "metrics"]}