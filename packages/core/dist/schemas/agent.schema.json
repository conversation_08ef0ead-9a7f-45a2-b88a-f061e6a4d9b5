{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent", "description": "Defines the configuration and capabilities of an AI agent.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "agent_id": {"type": "string", "description": "A unique identifier for the agent."}, "name": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)$"}, "status": {"type": "string", "enum": ["active", "inactive", "deprecated"]}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "capabilities": {"type": "array", "items": {"type": "string"}}, "tools": {"type": "array", "items": {"type": "object", "properties": {"tool_id": {"type": "string"}, "name": {"type": "string"}}, "required": ["tool_id", "name"]}}, "model_config": {"type": "object", "properties": {"provider": {"type": "string"}, "model": {"type": "string"}, "temperature": {"type": "number", "minimum": 0, "maximum": 1}, "max_tokens": {"type": "integer"}, "system_prompt": {"type": "string"}}, "required": ["provider", "model"]}, "performance_targets": {"type": "object", "properties": {"max_response_time_seconds": {"type": "integer"}, "min_confidence_score": {"type": "number", "minimum": 0, "maximum": 1}, "max_cost_per_execution": {"type": "number"}}}}, "required": ["agent_id", "name", "version", "status", "model_config"]}