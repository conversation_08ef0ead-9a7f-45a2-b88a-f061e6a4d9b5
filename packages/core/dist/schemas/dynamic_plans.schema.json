{"$schema": "http://json-schema.org/draft-07/schema#", "title": "DynamicPlan", "description": "Represents an agent's self-generated plan to achieve a complex goal.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "plan_id": {"type": "string"}, "agent_id": {"type": "string"}, "goal": {"type": "string"}, "status": {"type": "string", "enum": ["planning", "ready_for_execution", "executing", "completed", "failed"]}, "plan": {"type": "array", "items": {"type": "object", "properties": {"step": {"type": "integer"}, "thought": {"type": "string"}, "action": {"type": "string"}, "params": {"type": "object"}}, "required": ["step", "thought", "action"]}}, "validation_criteria": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["plan_id", "agent_id", "goal", "status", "plan"]}