{"$schema": "http://json-schema.org/draft-07/schema#", "title": "HumanFeedback", "description": "Captures explicit user feedback to correct and improve agent behavior.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "feedback_id": {"type": "string"}, "workflow_id": {"type": "string"}, "trace_id": {"type": "string"}, "user_id": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "feedback_type": {"type": "string", "enum": ["correction", "reinforcement"]}, "target_trace_step": {"type": "integer"}, "correction": {"type": "object", "properties": {"suggested_action": {"type": "string"}, "suggested_params": {"type": "object"}, "reasoning": {"type": "string"}}}, "rating": {"type": "integer", "minimum": 1, "maximum": 5}, "status": {"type": "string", "enum": ["pending", "processed", "rejected"]}}, "required": ["feedback_id", "user_id", "timestamp", "feedback_type"]}