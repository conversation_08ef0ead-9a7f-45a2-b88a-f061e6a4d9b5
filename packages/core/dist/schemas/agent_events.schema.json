{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentEvent", "description": "An immutable event representing a significant action or state change in the agent system.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "event_id": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "agent_id": {"type": "string"}, "workflow_id": {"type": "string"}, "trace_id": {"type": "string"}, "event_type": {"type": "string"}, "payload": {"type": "object", "description": "The data associated with the event."}}, "required": ["event_id", "timestamp", "agent_id", "event_type", "payload"]}