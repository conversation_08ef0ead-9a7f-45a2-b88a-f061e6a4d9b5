{"$schema": "http://json-schema.org/draft-07/schema#", "title": "IngestionPipeline", "description": "Defines a configurable pipeline for processing data from external sources.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "pipeline_id": {"type": "string"}, "source_type": {"type": "string"}, "source_config": {"type": "object"}, "trigger": {"type": "string", "enum": ["on_new_file", "scheduled"]}, "processing_steps": {"type": "array", "items": {"type": "object", "properties": {"step": {"type": "integer"}, "action": {"type": "string"}, "params": {"type": "object"}}, "required": ["step", "action"]}}, "status": {"type": "string", "enum": ["active", "paused", "error"]}, "last_run": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["success", "failed"]}}}}, "required": ["pipeline_id", "source_type", "trigger", "processing_steps", "status"]}