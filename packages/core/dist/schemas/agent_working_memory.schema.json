{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentWorkingMemory", "description": "Short-term working memory for an agent session with TTL expiration.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "session_id": {"type": "string", "description": "Unique session identifier."}, "agent_id": {"type": "string", "description": "Agent that owns this working memory."}, "created_at": {"type": "string", "format": "date-time"}, "expires_at": {"type": "string", "format": "date-time", "description": "TTL expiration timestamp."}, "context_window": {"type": "array", "description": "Current conversation context.", "items": {"type": "object", "properties": {"role": {"type": "string", "enum": ["user", "assistant", "system", "tool"]}, "content": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "tool_name": {"type": "string"}, "tool_input": {"type": "object"}, "tool_output": {"type": "object"}}, "required": ["role", "content", "timestamp"]}}, "working_state": {"type": "object", "description": "Current task state and progress.", "properties": {"current_task": {"type": "string"}, "progress": {"type": "number", "minimum": 0, "maximum": 1}, "next_action": {"type": "string"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "variables": {"type": "object"}}}, "temp_findings": {"type": "object", "description": "Temporary findings and intermediate results."}}, "required": ["session_id", "agent_id", "created_at", "expires_at", "context_window"]}