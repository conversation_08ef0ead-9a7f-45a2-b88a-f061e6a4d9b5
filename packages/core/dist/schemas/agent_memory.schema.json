{"$schema": "http://json-schema.org/draft-07/schema#", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Represents a single piece of memory for an AI agent.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "memory_id": {"type": "string"}, "agent_id": {"type": "string"}, "memory_type": {"type": "string", "enum": ["learned_pattern", "episodic", "semantic", "procedural"]}, "created_at": {"type": "string", "format": "date-time"}, "last_accessed": {"type": "string", "format": "date-time"}, "access_count": {"type": "integer"}, "content": {"type": "object", "properties": {"text": {"type": "string"}, "summary": {"type": "string"}, "confidence": {"type": "number"}}, "required": ["text"]}, "embedding": {"type": "object", "properties": {"values": {"type": "array", "items": {"type": "number"}}, "meta": {"type": "object", "properties": {"provider": {"type": "string"}, "model": {"type": "string"}, "version": {"type": "string"}}, "required": ["provider", "model", "version"]}}, "required": ["values", "meta"]}, "metadata": {"type": "object", "properties": {"domain": {"type": "string"}, "relevance_score": {"type": "number"}, "decay_factor": {"type": "number"}, "memory_strength": {"type": "string", "enum": ["weak", "medium", "strong"]}}}}, "required": ["memory_id", "agent_id", "memory_type", "content", "embedding"]}