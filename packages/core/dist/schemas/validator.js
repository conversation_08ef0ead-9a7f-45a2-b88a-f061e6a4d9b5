"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAgentEvents = exports.validateIngestionPipelines = exports.validateSecureCredentials = exports.validateResourceRegistry = exports.validateAgentPermissions = exports.validateHumanFeedback = exports.validateEvaluations = exports.validateDynamicPlans = exports.validateTraces = exports.validateToolExecutions = exports.validateVectorEmbeddings = exports.validateAgentWorkflows = exports.validateAgentPerformanceMetrics = exports.validateAgentTools = exports.validateAgentWorkingMemory = exports.validateAgentMemory = exports.validateAgentConfiguration = exports.validateAgent = exports.SchemaValidator = void 0;
const ajv_1 = __importDefault(require("ajv"));
const ajv_formats_1 = __importDefault(require("ajv-formats"));
const index_1 = require("./index");
// Initialize AJV with formats support
const ajv = new ajv_1.default({ allErrors: true, strict: false });
(0, ajv_formats_1.default)(ajv);
// Compile all schemas
const compiledSchemas = {};
Object.entries(index_1.schemas).forEach(([name, schema]) => {
    compiledSchemas[name] = ajv.compile(schema);
});
class SchemaValidator {
    /**
     * Validates data against a specific schema
     */
    static validate(schemaName, data) {
        const validator = compiledSchemas[schemaName];
        if (!validator) {
            throw new Error(`Schema '${schemaName}' not found. Available schemas: ${Object.keys(compiledSchemas).join(', ')}`);
        }
        const valid = validator(data);
        if (!valid) {
            const errors = validator.errors?.map((error) => {
                return `${error.instancePath || 'root'}: ${error.message}`;
            }) || [];
            return { valid: false, errors };
        }
        return { valid: true };
    }
    /**
     * Validates and throws an error if validation fails
     */
    static validateOrThrow(schemaName, data) {
        const result = this.validate(schemaName, data);
        if (!result.valid) {
            throw new Error(`Validation failed for schema '${schemaName}': ${result.errors?.join(', ')}`);
        }
    }
    /**
     * Gets all available schema names
     */
    static getAvailableSchemas() {
        return Object.keys(compiledSchemas);
    }
}
exports.SchemaValidator = SchemaValidator;
// Export individual validators for convenience
const validateAgent = (data) => SchemaValidator.validate('agent', data);
exports.validateAgent = validateAgent;
const validateAgentConfiguration = (data) => SchemaValidator.validate('agentConfigurations', data);
exports.validateAgentConfiguration = validateAgentConfiguration;
const validateAgentMemory = (data) => SchemaValidator.validate('agentMemory', data);
exports.validateAgentMemory = validateAgentMemory;
const validateAgentWorkingMemory = (data) => SchemaValidator.validate('agentWorkingMemory', data);
exports.validateAgentWorkingMemory = validateAgentWorkingMemory;
const validateAgentTools = (data) => SchemaValidator.validate('agentTools', data);
exports.validateAgentTools = validateAgentTools;
const validateAgentPerformanceMetrics = (data) => SchemaValidator.validate('agentPerformanceMetrics', data);
exports.validateAgentPerformanceMetrics = validateAgentPerformanceMetrics;
const validateAgentWorkflows = (data) => SchemaValidator.validate('agentWorkflows', data);
exports.validateAgentWorkflows = validateAgentWorkflows;
const validateVectorEmbeddings = (data) => SchemaValidator.validate('vectorEmbeddings', data);
exports.validateVectorEmbeddings = validateVectorEmbeddings;
const validateToolExecutions = (data) => SchemaValidator.validate('toolExecutions', data);
exports.validateToolExecutions = validateToolExecutions;
const validateTraces = (data) => SchemaValidator.validate('traces', data);
exports.validateTraces = validateTraces;
const validateDynamicPlans = (data) => SchemaValidator.validate('dynamicPlans', data);
exports.validateDynamicPlans = validateDynamicPlans;
const validateEvaluations = (data) => SchemaValidator.validate('evaluations', data);
exports.validateEvaluations = validateEvaluations;
const validateHumanFeedback = (data) => SchemaValidator.validate('humanFeedback', data);
exports.validateHumanFeedback = validateHumanFeedback;
const validateAgentPermissions = (data) => SchemaValidator.validate('agentPermissions', data);
exports.validateAgentPermissions = validateAgentPermissions;
const validateResourceRegistry = (data) => SchemaValidator.validate('resourceRegistry', data);
exports.validateResourceRegistry = validateResourceRegistry;
const validateSecureCredentials = (data) => SchemaValidator.validate('secureCredentials', data);
exports.validateSecureCredentials = validateSecureCredentials;
const validateIngestionPipelines = (data) => SchemaValidator.validate('ingestionPipelines', data);
exports.validateIngestionPipelines = validateIngestionPipelines;
const validateAgentEvents = (data) => SchemaValidator.validate('agentEvents', data);
exports.validateAgentEvents = validateAgentEvents;
