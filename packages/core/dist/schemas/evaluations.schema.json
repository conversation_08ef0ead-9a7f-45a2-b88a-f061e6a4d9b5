{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Evaluation", "description": "Stores the results of running a benchmark test against an agent version.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "evaluation_id": {"type": "string"}, "agent_id": {"type": "string"}, "agent_version": {"type": "string"}, "benchmark_id": {"type": "string"}, "executed_at": {"type": "string", "format": "date-time"}, "task_input": {"type": "object"}, "agent_output": {"type": "object"}, "ground_truth": {"type": "object"}, "scores": {"type": "object", "properties": {"correctness": {"type": "number"}, "completeness": {"type": "number"}, "cost_usd": {"type": "number"}, "latency_ms": {"type": "integer"}}, "required": ["correctness", "cost_usd", "latency_ms"]}, "passed": {"type": "boolean"}}, "required": ["evaluation_id", "agent_id", "agent_version", "benchmark_id", "executed_at", "scores", "passed"]}