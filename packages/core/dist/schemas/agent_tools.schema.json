{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AgentTool", "description": "Definition of a tool that can be used by AI agents.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "tool_id": {"type": "string", "description": "Unique identifier for the tool."}, "name": {"type": "string", "description": "Human-readable name of the tool."}, "description": {"type": "string", "description": "Description of what the tool does."}, "version": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)$"}, "status": {"type": "string", "enum": ["active", "inactive", "deprecated"]}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "config": {"type": "object", "description": "Tool configuration and connection details.", "properties": {"api_endpoint": {"type": "string", "format": "uri"}, "authentication": {"type": "object", "properties": {"type": {"type": "string", "enum": ["none", "api_key", "bearer_token", "oauth2"]}, "header_name": {"type": "string"}, "token_prefix": {"type": "string"}}, "required": ["type"]}, "default_parameters": {"type": "object"}, "timeout_seconds": {"type": "integer", "minimum": 1}}}, "input_schema": {"type": "object", "description": "JSON Schema defining the tool's input parameters.", "$ref": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"type": "object", "description": "JSON Schema defining the tool's output format.", "$ref": "http://json-schema.org/draft-07/schema#"}, "rate_limits": {"type": "object", "properties": {"calls_per_minute": {"type": "integer", "minimum": 0}, "calls_per_hour": {"type": "integer", "minimum": 0}, "calls_per_day": {"type": "integer", "minimum": 0}, "concurrent_calls": {"type": "integer", "minimum": 1}}}, "cost_model": {"type": "object", "properties": {"cost_per_call": {"type": "number", "minimum": 0}, "cost_per_token": {"type": "number", "minimum": 0}, "currency": {"type": "string", "default": "USD"}}}, "performance_stats": {"type": "object", "properties": {"total_calls": {"type": "integer", "minimum": 0}, "success_rate": {"type": "number", "minimum": 0, "maximum": 1}, "avg_response_time_ms": {"type": "number", "minimum": 0}, "avg_cost_per_call": {"type": "number", "minimum": 0}, "last_updated": {"type": "string", "format": "date-time"}}}}, "required": ["tool_id", "name", "description", "version", "status", "input_schema", "output_schema"]}