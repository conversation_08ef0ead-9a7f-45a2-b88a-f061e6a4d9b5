export declare const schemas: {
    agent: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            agent_id: {
                type: string;
                description: string;
            };
            name: {
                type: string;
            };
            description: {
                type: string;
            };
            version: {
                type: string;
                pattern: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            updated_at: {
                type: string;
                format: string;
            };
            capabilities: {
                type: string;
                items: {
                    type: string;
                };
            };
            tools: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        tool_id: {
                            type: string;
                        };
                        name: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
            model_config: {
                type: string;
                properties: {
                    provider: {
                        type: string;
                    };
                    model: {
                        type: string;
                    };
                    temperature: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    max_tokens: {
                        type: string;
                    };
                    system_prompt: {
                        type: string;
                    };
                };
                required: string[];
            };
            performance_targets: {
                type: string;
                properties: {
                    max_response_time_seconds: {
                        type: string;
                    };
                    min_confidence_score: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    max_cost_per_execution: {
                        type: string;
                    };
                };
            };
        };
        required: string[];
    };
    agentConfigurations: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            config_id: {
                type: string;
                description: string;
            };
            agent_id: {
                type: string;
                description: string;
            };
            version: {
                type: string;
                description: string;
            };
            is_active: {
                type: string;
                description: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            prompts: {
                type: string;
                description: string;
                properties: {
                    system_prompt: {
                        type: string;
                    };
                    task_prompt: {
                        type: string;
                    };
                    analysis_prompt: {
                        type: string;
                    };
                    fallback_prompt: {
                        type: string;
                    };
                };
                required: string[];
            };
            parameters: {
                type: string;
                description: string;
                properties: {
                    confidence_threshold: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    max_iterations: {
                        type: string;
                        minimum: number;
                    };
                    temperature: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    max_tokens: {
                        type: string;
                        minimum: number;
                    };
                };
            };
            quality_gates: {
                type: string;
                description: string;
                properties: {
                    min_confidence: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    required_fields: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                    validation_rules: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                };
            };
            network_policies: {
                type: string;
                description: string;
                properties: {
                    allowed_hosts: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                    blocked_hosts: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                    require_approval: {
                        type: string;
                    };
                };
            };
        };
        required: string[];
    };
    agentMemory: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            memory_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            memory_type: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            last_accessed: {
                type: string;
                format: string;
            };
            access_count: {
                type: string;
            };
            content: {
                type: string;
                properties: {
                    text: {
                        type: string;
                    };
                    summary: {
                        type: string;
                    };
                    confidence: {
                        type: string;
                    };
                };
                required: string[];
            };
            embedding: {
                type: string;
                properties: {
                    values: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                    meta: {
                        type: string;
                        properties: {
                            provider: {
                                type: string;
                            };
                            model: {
                                type: string;
                            };
                            version: {
                                type: string;
                            };
                        };
                        required: string[];
                    };
                };
                required: string[];
            };
            metadata: {
                type: string;
                properties: {
                    domain: {
                        type: string;
                    };
                    relevance_score: {
                        type: string;
                    };
                    decay_factor: {
                        type: string;
                    };
                    memory_strength: {
                        type: string;
                        enum: string[];
                    };
                };
            };
        };
        required: string[];
    };
    agentWorkingMemory: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            session_id: {
                type: string;
                description: string;
            };
            agent_id: {
                type: string;
                description: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            expires_at: {
                type: string;
                format: string;
                description: string;
            };
            context_window: {
                type: string;
                description: string;
                items: {
                    type: string;
                    properties: {
                        role: {
                            type: string;
                            enum: string[];
                        };
                        content: {
                            type: string;
                        };
                        timestamp: {
                            type: string;
                            format: string;
                        };
                        tool_name: {
                            type: string;
                        };
                        tool_input: {
                            type: string;
                        };
                        tool_output: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
            working_state: {
                type: string;
                description: string;
                properties: {
                    current_task: {
                        type: string;
                    };
                    progress: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    next_action: {
                        type: string;
                    };
                    confidence: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    variables: {
                        type: string;
                    };
                };
            };
            temp_findings: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    agentTools: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            tool_id: {
                type: string;
                description: string;
            };
            name: {
                type: string;
                description: string;
            };
            description: {
                type: string;
                description: string;
            };
            version: {
                type: string;
                pattern: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            updated_at: {
                type: string;
                format: string;
            };
            config: {
                type: string;
                description: string;
                properties: {
                    api_endpoint: {
                        type: string;
                        format: string;
                    };
                    authentication: {
                        type: string;
                        properties: {
                            type: {
                                type: string;
                                enum: string[];
                            };
                            header_name: {
                                type: string;
                            };
                            token_prefix: {
                                type: string;
                            };
                        };
                        required: string[];
                    };
                    default_parameters: {
                        type: string;
                    };
                    timeout_seconds: {
                        type: string;
                        minimum: number;
                    };
                };
            };
            input_schema: {
                type: string;
                description: string;
                $ref: string;
            };
            output_schema: {
                type: string;
                description: string;
                $ref: string;
            };
            rate_limits: {
                type: string;
                properties: {
                    calls_per_minute: {
                        type: string;
                        minimum: number;
                    };
                    calls_per_hour: {
                        type: string;
                        minimum: number;
                    };
                    calls_per_day: {
                        type: string;
                        minimum: number;
                    };
                    concurrent_calls: {
                        type: string;
                        minimum: number;
                    };
                };
            };
            cost_model: {
                type: string;
                properties: {
                    cost_per_call: {
                        type: string;
                        minimum: number;
                    };
                    cost_per_token: {
                        type: string;
                        minimum: number;
                    };
                    currency: {
                        type: string;
                        default: string;
                    };
                };
            };
            performance_stats: {
                type: string;
                properties: {
                    total_calls: {
                        type: string;
                        minimum: number;
                    };
                    success_rate: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    avg_response_time_ms: {
                        type: string;
                        minimum: number;
                    };
                    avg_cost_per_call: {
                        type: string;
                        minimum: number;
                    };
                    last_updated: {
                        type: string;
                        format: string;
                    };
                };
            };
        };
        required: string[];
    };
    agentPerformanceMetrics: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            metric_id: {
                type: string;
                description: string;
            };
            agent_id: {
                type: string;
                description: string;
            };
            timestamp: {
                type: string;
                format: string;
                description: string;
            };
            time_window: {
                type: string;
                enum: string[];
                description: string;
            };
            metrics: {
                type: string;
                description: string;
                properties: {
                    tasks_completed: {
                        type: string;
                        minimum: number;
                    };
                    tasks_failed: {
                        type: string;
                        minimum: number;
                    };
                    success_rate: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    avg_response_time_seconds: {
                        type: string;
                        minimum: number;
                    };
                    median_response_time_seconds: {
                        type: string;
                        minimum: number;
                    };
                    p95_response_time_seconds: {
                        type: string;
                        minimum: number;
                    };
                    total_cost_usd: {
                        type: string;
                        minimum: number;
                    };
                    avg_cost_per_task: {
                        type: string;
                        minimum: number;
                    };
                    total_tokens_used: {
                        type: string;
                        minimum: number;
                    };
                    avg_tokens_per_task: {
                        type: string;
                        minimum: number;
                    };
                };
                required: string[];
            };
            quality: {
                type: string;
                description: string;
                properties: {
                    avg_confidence_score: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    min_confidence_score: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    max_confidence_score: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    user_satisfaction_score: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    data_completeness_rate: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                };
            };
            resources: {
                type: string;
                description: string;
                properties: {
                    memory_usage_mb: {
                        type: string;
                        minimum: number;
                    };
                    cpu_usage_percent: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                    network_requests: {
                        type: string;
                        minimum: number;
                    };
                    cache_hit_rate: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                };
            };
            errors: {
                type: string;
                description: string;
                properties: {
                    total_errors: {
                        type: string;
                        minimum: number;
                    };
                    error_types: {
                        type: string;
                        additionalProperties: {
                            type: string;
                            minimum: number;
                        };
                    };
                    error_rate: {
                        type: string;
                        minimum: number;
                        maximum: number;
                    };
                };
            };
        };
        required: string[];
    };
    agentWorkflows: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            workflow_id: {
                type: string;
            };
            workflow_name: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            updated_at: {
                type: string;
                format: string;
            };
            current_step: {
                type: string;
            };
            workflow_definition: {
                type: string;
                properties: {
                    steps: {
                        type: string;
                        items: {
                            type: string;
                            properties: {
                                step_id: {
                                    type: string;
                                };
                                agent_id: {
                                    type: string;
                                };
                                description: {
                                    type: string;
                                };
                                depends_on: {
                                    type: string;
                                    items: {
                                        type: string;
                                    };
                                };
                                timeout_seconds: {
                                    type: string;
                                };
                                retry_count: {
                                    type: string;
                                };
                            };
                            required: string[];
                        };
                    };
                };
                required: string[];
            };
            execution_log: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        step_id: {
                            type: string;
                        };
                        status: {
                            type: string;
                        };
                        started_at: {
                            type: string;
                            format: string;
                        };
                        completed_at: {
                            type: string;
                            format: string;
                        };
                        output: {
                            type: string;
                        };
                    };
                };
            };
            shared_context: {
                type: string;
            };
        };
        required: string[];
    };
    vectorEmbeddings: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            embedding_id: {
                type: string;
            };
            source_type: {
                type: string;
            };
            source_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            embedding: {
                type: string;
                properties: {
                    values: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                    meta: {
                        type: string;
                        properties: {
                            provider: {
                                type: string;
                            };
                            model: {
                                type: string;
                            };
                            version: {
                                type: string;
                            };
                        };
                        required: string[];
                    };
                };
                required: string[];
            };
            content: {
                type: string;
                properties: {
                    text: {
                        type: string;
                    };
                    summary: {
                        type: string;
                    };
                };
                required: string[];
            };
            metadata: {
                type: string;
            };
        };
        required: string[];
    };
    toolExecutions: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            execution_id: {
                type: string;
            };
            tool_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            executed_at: {
                type: string;
                format: string;
            };
            input: {
                type: string;
            };
            output: {
                type: string;
            };
            performance: {
                type: string;
                properties: {
                    execution_time_ms: {
                        type: string;
                    };
                    success: {
                        type: string;
                    };
                    cost: {
                        type: string;
                    };
                };
                required: string[];
            };
            error: {
                type: string;
                properties: {
                    message: {
                        type: string;
                    };
                    stack: {
                        type: string;
                    };
                };
            };
        };
        required: string[];
    };
    traces: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            trace_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            plan_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            trace_data: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        type: {
                            type: string;
                            enum: string[];
                        };
                        content: {
                            type: string[];
                        };
                        tool: {
                            type: string;
                        };
                        input: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
        };
        required: string[];
    };
    dynamicPlans: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            plan_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            goal: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            plan: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        step: {
                            type: string;
                        };
                        thought: {
                            type: string;
                        };
                        action: {
                            type: string;
                        };
                        params: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
            validation_criteria: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
        };
        required: string[];
    };
    evaluations: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            evaluation_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            agent_version: {
                type: string;
            };
            benchmark_id: {
                type: string;
            };
            executed_at: {
                type: string;
                format: string;
            };
            task_input: {
                type: string;
            };
            agent_output: {
                type: string;
            };
            ground_truth: {
                type: string;
            };
            scores: {
                type: string;
                properties: {
                    correctness: {
                        type: string;
                    };
                    completeness: {
                        type: string;
                    };
                    cost_usd: {
                        type: string;
                    };
                    latency_ms: {
                        type: string;
                    };
                };
                required: string[];
            };
            passed: {
                type: string;
            };
        };
        required: string[];
    };
    humanFeedback: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            feedback_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            trace_id: {
                type: string;
            };
            user_id: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            feedback_type: {
                type: string;
                enum: string[];
            };
            target_trace_step: {
                type: string;
            };
            correction: {
                type: string;
                properties: {
                    suggested_action: {
                        type: string;
                    };
                    suggested_params: {
                        type: string;
                    };
                    reasoning: {
                        type: string;
                    };
                };
            };
            rating: {
                type: string;
                minimum: number;
                maximum: number;
            };
            status: {
                type: string;
                enum: string[];
            };
        };
        required: string[];
    };
    agentPermissions: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            agent_id: {
                type: string;
            };
            permissions: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        tool_id: {
                            type: string;
                        };
                        policy: {
                            type: string;
                            enum: string[];
                        };
                        approver_group: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
        };
        required: string[];
    };
    resourceRegistry: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            resource_id: {
                type: string;
            };
            resource_type: {
                type: string;
                enum: string[];
            };
            name: {
                type: string;
            };
            description: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            access_point: {
                type: string;
                format: string;
            };
            authentication_method: {
                type: string;
            };
            credential_id: {
                type: string;
            };
            metadata: {
                type: string;
            };
        };
        required: string[];
    };
    secureCredentials: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            credential_id: {
                type: string;
            };
            resource_id: {
                type: string;
            };
            credential_type: {
                type: string;
            };
            encrypted_credentials: {
                type: string;
                description: string;
            };
            last_rotated: {
                type: string;
                format: string;
            };
            status: {
                type: string;
                enum: string[];
            };
        };
        required: string[];
    };
    ingestionPipelines: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            pipeline_id: {
                type: string;
            };
            source_type: {
                type: string;
            };
            source_config: {
                type: string;
            };
            trigger: {
                type: string;
                enum: string[];
            };
            processing_steps: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        step: {
                            type: string;
                        };
                        action: {
                            type: string;
                        };
                        params: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
            status: {
                type: string;
                enum: string[];
            };
            last_run: {
                type: string;
                properties: {
                    timestamp: {
                        type: string;
                        format: string;
                    };
                    status: {
                        type: string;
                        enum: string[];
                    };
                };
            };
        };
        required: string[];
    };
    agentEvents: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            event_id: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            agent_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            trace_id: {
                type: string;
            };
            event_type: {
                type: string;
            };
            payload: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
};
//# sourceMappingURL=index.d.ts.map