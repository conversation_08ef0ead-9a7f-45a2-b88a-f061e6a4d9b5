{"$schema": "http://json-schema.org/draft-07/schema#", "title": "VectorEmbedding", "description": "Stores a vector embedding and its associated content and metadata.", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "embedding_id": {"type": "string"}, "source_type": {"type": "string"}, "source_id": {"type": "string"}, "agent_id": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "embedding": {"type": "object", "properties": {"values": {"type": "array", "items": {"type": "number"}}, "meta": {"type": "object", "properties": {"provider": {"type": "string"}, "model": {"type": "string"}, "version": {"type": "string"}}, "required": ["provider", "model", "version"]}}, "required": ["values", "meta"]}, "content": {"type": "object", "properties": {"text": {"type": "string"}, "summary": {"type": "string"}}, "required": ["text"]}, "metadata": {"type": "object"}}, "required": ["embedding_id", "source_type", "source_id", "embedding", "content"]}